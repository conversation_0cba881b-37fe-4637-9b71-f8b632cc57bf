import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/components/admin/AdminLayout';
import { toast } from 'react-toastify';

const PaymentMethodsEditor = () => {
  const [paymentMethodsData, setPaymentMethodsData] = useState({
    title: '',
    logos: []
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchPaymentMethodsData();
  }, []);

  const fetchPaymentMethodsData = async () => {
    try {
      const response = await fetch('/api/content/pricing?section=paymentMethods');
      if (response.ok) {
        const data = await response.json();
        setPaymentMethodsData(data);
      } else {
        setError('Failed to fetch payment methods data');
      }
    } catch (error) {
      console.error('Error fetching payment methods data:', error);
      setError('Failed to fetch payment methods data');
    } finally {
      setLoading(false);
    }
  };

  const handleTitleChange = (e) => {
    setPaymentMethodsData(prev => ({
      ...prev,
      title: e.target.value
    }));
  };

  const handleImageUpload = async (index, event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploadingImage(index);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'payment-methods');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const result = await response.json();

      if (result && result.secure_url) {
        const newLogos = [...paymentMethodsData.logos];
        newLogos[index] = result.secure_url;

        setPaymentMethodsData(prev => ({
          ...prev,
          logos: newLogos
        }));

        toast.success('Image uploaded successfully');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Failed to upload image');
      toast.error('Failed to upload image');
    } finally {
      setUploadingImage(null);
    }
  };

  const handleAddLogo = () => {
    setPaymentMethodsData(prev => ({
      ...prev,
      logos: [...prev.logos, '']
    }));
  };

  const handleRemoveLogo = (index) => {
    setPaymentMethodsData(prev => ({
      ...prev,
      logos: prev.logos.filter((_, i) => i !== index)
    }));
  };

  const handleSave = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      // Filter out any empty logo entries and ensure they're strings
      const validLogos = paymentMethodsData.logos
        .filter(logo => {
          if (!logo) return false;
          if (typeof logo === 'string') return logo.trim() !== '';
          return false;
        })
        .map(logo => logo.trim());
      
      if (validLogos.length === 0) {
        toast.warning('Please add at least one payment method logo');
        return;
      }
      
      // Prepare data to save
      const dataToSave = {
        title: paymentMethodsData.title.trim(),
        logos: validLogos
      };

      const response = await fetch('/api/content/pricing?section=paymentMethods', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(dataToSave)
      });

      if (response.ok) {
        toast.success('Payment methods updated successfully');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save payment methods');
      }
    } catch (error) {
      console.error('Error saving payment methods:', error);
      setError(error.message);
      toast.error('Failed to save payment methods');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="admin-editor__loading">
          <p>Loading payment methods data...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Payment Methods | Photodit Admin</title>
      </Head>

      <div className="admin-editor">
        <div className="admin-editor__header">
          <h1 className="admin-editor__title">Payment Methods Management</h1>
          <div className="admin-editor__actions">
            <Link href="/admin/pricing" className="admin-editor__back-button">
              Back to Pricing
            </Link>
            <button 
              type="button" 
              className="admin-editor__save-button"
              onClick={handleSave}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>

        {error && (
          <div className="admin-editor__error">
            {error}
          </div>
        )}

        <div className="admin-editor__content">
          <div className="admin-editor__section">
            <h2 className="admin-editor__section-title">Payment Methods Settings</h2>
            
            <div className="admin-editor__field">
              <label className="admin-editor__label">Section Title</label>
              <input
                type="text"
                className="admin-editor__input"
                value={paymentMethodsData.title}
                onChange={handleTitleChange}
                placeholder="Enter section title"
              />
            </div>
          </div>

          <div className="admin-editor__section">
            <div className="admin-editor__section-header">
              <h2 className="admin-editor__section-title">Payment Method Logos</h2>
              <button 
                type="button" 
                className="admin-editor__add-button"
                onClick={handleAddLogo}
              >
                Add Logo
              </button>
            </div>
            
            <div className="logos-grid">
              {paymentMethodsData.logos.map((logo, index) => (
                <div key={index} className="logo-editor">
                  <div className="logo-editor__preview">
                    {logo && (
                      <Image 
                        src={logo} 
                        alt={`Payment Method ${index + 1}`} 
                        width={120} 
                        height={80} 
                      />
                    )}
                  </div>
                  <div className="logo-editor__actions">
                    <div className="logo-editor__upload">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleImageUpload(index, e)}
                        className="form-control-file"
                      />
                      {uploadingImage === index && <span>Uploading...</span>}
                    </div>
                    <button 
                      type="button" 
                      className="btn btn-sm btn-danger"
                      onClick={() => handleRemoveLogo(index)}
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .admin-editor__loading {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200px;
          font-size: 18px;
          color: #64748b;
        }

        .admin-editor__error {
          background-color: #fee2e2;
          color: #b91c1c;
          padding: 12px;
          border-radius: 6px;
          margin-bottom: 20px;
        }

        .logos-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 20px;
          margin-top: 20px;
        }

        .logo-editor {
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 16px;
          background: white;
        }

        .logo-editor__preview {
          width: 100%;
          height: 120px;
          border: 2px dashed #cbd5e1;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12px;
          background-color: #f8fafc;
        }

        .logo-editor__actions {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .logo-editor__upload {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .form-control-file {
          padding: 8px;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          font-size: 14px;
        }

        .btn {
          padding: 6px 12px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        }

        .btn-danger {
          background-color: #dc2626;
          color: white;
        }

        .btn-danger:hover {
          background-color: #b91c1c;
        }
      `}</style>
    </AdminLayout>
  );
};

export default PaymentMethodsEditor;
