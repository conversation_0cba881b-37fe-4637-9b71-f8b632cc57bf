/* Admin Panel Standardized Styles */

/* Common Layout */
.admin-page {
  width: 100%;
}

/* Page Header */
.admin-page__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  position: relative;
}

.admin-page__title-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.admin-page__back-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background-color: #f1f5f9;
  color: #4b5563;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.admin-page__back-link:hover {
  background-color: #e2e8f0;
  transform: translateX(-4px);
}

.admin-page__title {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  color: #1e293b;
  position: relative;
}

.admin-page__title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #4569e7;
  border-radius: 2px;
}

.admin-page__description {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 24px;
  max-width: 600px;
  line-height: 1.6;
}

/* Action Buttons */
.admin-page__save-button {
  padding: 12px 24px;
  background-color: #4569e7;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(69, 105, 231, 0.25);
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-page__save-button::before {
  content: '💾';
  font-size: 16px;
}

.admin-page__save-button:hover {
  background-color: #3a5bc7;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(69, 105, 231, 0.3);
}

.admin-page__save-button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.admin-page__add-button {
  padding: 10px 20px;
  background-color: #4569e7;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(69, 105, 231, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-page__add-button::before {
  content: '➕';
  font-size: 14px;
}

.admin-page__add-button:hover {
  background-color: #3a5bc7;
  transform: translateY(-2px);
}

.admin-page__remove-button {
  padding: 6px 12px;
  background-color: #fee2e2;
  color: #b91c1c;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.admin-page__remove-button::before {
  content: '🗑️';
  font-size: 13px;
}

.admin-page__remove-button:hover {
  background-color: #fecaca;
  transform: translateY(-1px);
}

/* Content Sections */
.admin-page__content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.admin-page__section {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 32px;
  border: 1px solid #f1f5f9;
  transition: all 0.3s ease;
}

.admin-page__section:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border-color: #e2e8f0;
}

.admin-page__section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.admin-page__section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

/* Form Elements */
.admin-page__field {
  margin-bottom: 24px;
}

.admin-page__label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #475569;
  margin-bottom: 8px;
}

.admin-page__input,
.admin-page__textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.admin-page__input:focus,
.admin-page__textarea:focus {
  outline: none;
  border-color: #4569e7;
  box-shadow: 0 0 0 3px rgba(69, 105, 231, 0.1);
}

.admin-page__textarea {
  resize: vertical;
  min-height: 100px;
}

.admin-page__help-text {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-top: 6px;
}

/* Cards Grid */
.admin-page__cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.admin-page__card {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
  overflow: hidden;
  transition: all 0.3s ease;
}

.admin-page__card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
  border-color: #e2e8f0;
}

.admin-page__card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.admin-page__card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.admin-page__card-content {
  padding: 20px;
}

/* Status Messages */
.admin-page__error,
.admin-page__success {
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-page__error {
  background-color: #fee2e2;
  color: #b91c1c;
  border-left: 4px solid #ef4444;
}

.admin-page__success {
  background-color: #dcfce7;
  color: #166534;
  border-left: 4px solid #22c55e;
}

.admin-page__error p,
.admin-page__success p {
  margin: 0;
  font-weight: 500;
}

.admin-page__error p::before {
  content: '❌ ';
}

.admin-page__success p::before {
  content: '✅ ';
}

/* Loading State */
.admin-page__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.admin-page__loading::after {
  content: '';
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top-color: #4569e7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-top: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Modal Styles */
.admin-page__modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.admin-page__modal {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-page__modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.admin-page__modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.admin-page__modal-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #64748b;
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.admin-page__modal-close:hover {
  background-color: #f1f5f9;
  color: #1e293b;
}

.admin-page__modal-content {
  padding: 24px;
}

.admin-page__modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.admin-page__modal-cancel {
  padding: 10px 16px;
  background-color: #f1f5f9;
  color: #475569;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-page__modal-cancel:hover {
  background-color: #e2e8f0;
}

.admin-page__modal-save {
  padding: 10px 20px;
  background-color: #4569e7;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(69, 105, 231, 0.2);
}

.admin-page__modal-save:hover {
  background-color: #3a5bc7;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(69, 105, 231, 0.3);
}

.admin-page__modal-save:disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* File Upload Styles */
.admin-page__file-upload {
  margin-bottom: 16px;
}

.admin-page__file-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.admin-page__file-label {
  display: inline-flex;
  align-items: center;
}

/* Team Members Admin Page Styles */
.admin-page__members-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.admin-page__member-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.admin-page__member-card--highlight {
  box-shadow: 0 0 0 3px #4a90e2, 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: highlight-pulse 2s ease;
}

@keyframes highlight-pulse {
  0% { box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.5), 0 4px 12px rgba(0, 0, 0, 0.15); }
  50% { box-shadow: 0 0 0 5px rgba(74, 144, 226, 0.8), 0 4px 12px rgba(0, 0, 0, 0.15); }
  100% { box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.5), 0 4px 12px rgba(0, 0, 0, 0.15); }
}

.admin-page__member-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}

@media (min-width: 768px) {
  .admin-page__member-layout {
    flex-direction: row;
    align-items: flex-start;
  }

  .admin-page__member-image {
    width: 220px;
    flex-shrink: 0;
  }

  .admin-page__member-details {
    flex-grow: 1;
  }
}

.admin-page__member-index {
  display: inline-block;
  margin-left: 8px;
  font-size: 14px;
  color: #666;
  background-color: #e9ecef;
  padding: 2px 8px;
  border-radius: 12px;
}

.admin-page__file-label {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #f1f5f9;
  color: #475569;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.admin-page__file-label:hover {
  background-color: #e2e8f0;
  color: #1e293b;
}

.admin-page__image-preview {
  margin-top: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  max-height: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8fafc;
}

/* Project Specific Styles */
.admin-page__project-image {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.admin-page__project-details {
  margin-bottom: 16px;
}

.admin-page__project-details p {
  margin: 8px 0;
  font-size: 14px;
  color: #475569;
}

.admin-page__project-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.admin-page__edit-button {
  padding: 8px 16px;
  background-color: #e0f2fe;
  color: #0369a1;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.admin-page__edit-button:hover {
  background-color: #bae6fd;
}

/* Pricing Plan Specific Styles */
.admin-page__card--recommended {
  border: 2px solid #4569e7;
  position: relative;
}

.admin-page__card-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #4569e7;
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-bottom-left-radius: 8px;
  font-weight: 500;
  z-index: 1;
}

.admin-page__plan-price {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin: 16px 0;
}

.admin-page__plan-price-amount {
  font-size: 24px;
  font-weight: 700;
  color: #4569e7;
}

.admin-page__plan-price-unit {
  font-size: 14px;
  color: #64748b;
}

.admin-page__plan-description {
  margin-bottom: 16px;
  font-size: 14px;
  color: #334155;
}

.admin-page__plan-features {
  margin-bottom: 16px;
}

.admin-page__plan-features-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.admin-page__plan-features-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.admin-page__plan-feature {
  font-size: 14px;
  color: #334155;
  padding: 4px 0;
  position: relative;
  padding-left: 20px;
}

.admin-page__plan-feature:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #10b981;
}

/* Field Row for Side-by-Side Fields */
.admin-page__field-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

/* Checkbox Field */
.admin-page__field--checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.admin-page__checkbox {
  width: 16px;
  height: 16px;
}

.admin-page__checkbox-label {
  font-size: 14px;
  color: #1e293b;
  cursor: pointer;
}

/* Features List in Modal */
.admin-page__features-list {
  margin-bottom: 16px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px;
  background-color: #f8fafc;
}

.admin-page__feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f1f5f9;
  border-radius: 4px;
  margin-bottom: 8px;
}

.admin-page__feature-text {
  font-size: 14px;
  color: #334155;
}

.admin-page__feature-remove {
  background: none;
  border: none;
  color: #ef4444;
  font-size: 18px;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.admin-page__feature-remove:hover {
  background-color: #fee2e2;
}

.admin-page__feature-add {
  display: flex;
  gap: 8px;
}

.admin-page__feature-add .admin-page__input {
  flex: 1;
}

.admin-page__feature-add-button {
  padding: 8px 16px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.admin-page__feature-add-button:hover {
  background-color: #059669;
}

/* Portfolio Specific Styles */
.admin-page__portfolio-images {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.admin-page__portfolio-image {
  position: relative;
  flex: 1;
  height: 150px;
  background-color: #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-page__image-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  text-align: center;
  font-weight: 500;
}

.admin-page__portfolio-details {
  margin-bottom: 16px;
}

.admin-page__portfolio-details p {
  margin: 8px 0;
  font-size: 14px;
  color: #475569;
}

/* Select Input Styles */
.admin-page__select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  color: #1e293b;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23475569' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
}

.admin-page__select:focus {
  outline: none;
  border-color: #4569e7;
  box-shadow: 0 0 0 3px rgba(69, 105, 231, 0.1);
}

/* Image Uploader Styles */
.admin-page__image-uploader {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.admin-page__image-preview-container {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
  transition: all 0.2s ease;
}

.admin-page__image-preview-container:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-page__image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.admin-page__preview-image {
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
}

.admin-page__image-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #e2e8f0;
  color: #64748b;
  font-size: 14px;
  border-radius: 8px;
  gap: 8px;
  padding: 20px;
}

.admin-page__image-placeholder i {
  font-size: 24px;
  color: #94a3b8;
}

.admin-page__image-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.admin-page__small-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
  font-weight: 500;
}

.admin-page__url-input {
  margin-top: 4px;
}

.admin-page__error-message {
  color: #dc2626;
  font-size: 13px;
  margin-top: 4px;
  padding: 8px 12px;
  background-color: #fee2e2;
  border-radius: 6px;
  border-left: 3px solid #dc2626;
}

/* Settings Styles */
.admin-page__settings-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.admin-page__help-text {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
}

.admin-page__preview {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.admin-page__preview-item {
  display: flex;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.admin-page__preview-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.admin-page__preview-label {
  font-weight: 500;
  color: #64748b;
  width: 180px;
  flex-shrink: 0;
}

.admin-page__preview-value {
  color: #1e293b;
}

/* Email Settings Styles */
.admin-page__notice {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.admin-page__notice--info {
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
}

.admin-page__notice--warning {
  background-color: #fffbeb;
  border: 1px solid #fcd34d;
}

.admin-page__notice p {
  font-size: 14px;
  margin-bottom: 8px;
}

.admin-page__notice--info p {
  color: #0369a1;
}

.admin-page__notice--warning p {
  color: #b45309;
}

.admin-page__env-details {
  margin-top: 12px;
}

.admin-page__env-details ul {
  list-style-type: disc;
  margin-left: 20px;
  font-size: 13px;
}

.admin-page__notice--info .admin-page__env-details ul {
  color: #0369a1;
}

.admin-page__checkbox-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-page__checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.admin-page__checkbox-label {
  font-size: 14px;
  color: #1e293b;
  cursor: pointer;
}

.admin-page__test-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.admin-page__test-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.admin-page__test-button:hover {
  background-color: #059669;
}

.admin-page__test-button:disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
}

/* Section Editor Styles */
.admin-page__section-content {
  padding: 16px;
}

.admin-page__preview-mode {
  opacity: 0.9;
}

.admin-page__button-group {
  display: flex;
  gap: 8px;
}

.admin-page__cancel-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #f1f5f9;
  color: #475569;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-page__cancel-button:hover {
  background-color: #e2e8f0;
  color: #1e293b;
}

.admin-page__cancel-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .admin-page__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .admin-page__save-button {
    width: 100%;
    justify-content: center;
  }

  .admin-page__section {
    padding: 24px 16px;
  }
}
