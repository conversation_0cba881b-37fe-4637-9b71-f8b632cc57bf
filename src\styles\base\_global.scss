/* ==== 
 --------- (2.3) global styles start ---------
 ==== */

img {
  max-width: 100%;
  height: auto;
  border: 0px;
  outline: 0px;
}

i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.unset {
  max-width: unset;
}

.dir-rtl {
  direction: rtl;
}

.bg-img {
  @include background($theme-color, no-repeat, cover, center center);
}

.slick-slide {
  margin: 0px 15px;

  img {
    display: inline-block;
  }
}

.slick-list {
  margin: 0px -15px;
}

.gaper {
  row-gap: 30px;
}

.section {
  padding: 130px 0px;
}

.section--space-top {
  padding-bottom: 0px;
}

.section--space-bottom {
  padding-top: 0px;
}

.banner__content-cta {
  margin-top: 40px;
}

.section__cta {
  margin-top: 60px;
  text-align: center;
}

.section__content-cta {
  margin-top: 65px;
}

.banner__content {
  margin-top: -8px;
  text-align: center;

  .h6 {
    letter-spacing: 0.15em;
    text-transform: uppercase;
    margin-bottom: 16px;
    color: $theme-color;
    margin-bottom: 22px;
  }

  .h1 {
    font-weight: 700;
    color: $theme-color;
    margin-bottom: 16px;
    text-transform: capitalize;
  }
}

.cta__group {
  justify-content: center;
  margin-top: 50px !important;
}

.section__header {
  margin-top: -6px;
  margin-bottom: 65px;
  .sub-title {
    margin-bottom: 20px;
  }
}

.section__header--alt {
  margin-bottom: 65px;
  row-gap: 24px;
  .section__header {
    margin-bottom: 0px;
  }
}

.section__content {
  margin-top: -6px;
  .h6 {
    margin-bottom: 20px;
  }

  .h2 {
    margin-bottom: 30px;
  }
}

.social {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;

  li {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  a {
    color: $tertiary-color;
    background-color: #f0efff;
    @include box(40px);
    font-size: 16px;
    &:hover {
      color: $primary-color;
      background-color: $tertiary-color;
    }
  }
}

.bg-alt {
  background-color: #f4f7ff;
}

.fw-4 {
  font-weight: 400;
}

.fw-5 {
  font-weight: 500;
}

.fw-6 {
  font-weight: 600;
}

.fw-7 {
  font-weight: 700;
}

.sub-title {
  text-transform: uppercase;
  letter-spacing: 0.15em;
  color: $primary-color;
  line-height: 26px;
}

.title {
  text-transform: capitalize;
}

.paragraph {
  p {
    margin-bottom: 30px;
    text-transform: capitalize;
    color: $theme-color;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
  }
}

.cta__group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 65px;
}

.swiper-pagination-horizontal {
  display: flex;
  gap: 10px;
  align-items: center;
  button {
    text-indent: -9999px;
  }

  .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background-color: $tertiary-color;
    border-radius: 0px;
    opacity: 1 !important;
  }
  .swiper-pagination-bullet-active {
    outline: 1px solid $tertiary-color;
    outline-offset: 6px;
    background-color: $tertiary-color;
    margin: 0px 6px;
  }
}

.sidedot {
  width: 22px;
  height: 22px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-items: center;
  align-items: center;
  transition: $transition;
  transform: rotate(0deg);
  cursor: pointer;
  &:hover {
    transform: rotate(135deg);
  }
  span {
    width: 6px;
    display: inline-block;
    height: 6px;
    border-radius: 50%;
    background-color: $tertiary-color;
  }
}

.sidedot-active {
  transform: rotate(135deg);
}

.sidedot-two {
  @include box(46px);
  background-color: $white;
  outline-offset: 7px;
  outline: 1px solid $white;
  margin: 7px;
  .sidedot-inner {
    width: 22px;
    height: 22px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    justify-items: center;
    align-items: center;
    transition: $transition;
    transform: rotate(0deg);
    cursor: pointer;

    span {
      width: 6px;
      display: inline-block;
      height: 6px;
      border-radius: 50%;
      background-color: $primary-color;
    }
  }
  &:hover,
  &:focus {
    outline: 1px solid $white !important;
    .sidedot-inner {
      transform: rotate(135deg);
    }
  }
}

.sidedot-three {
  @include box(46px);
  background-color: rgba(255, 255, 255, 0.25);
  .sidedot-inner {
    width: 22px;
    height: 22px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    justify-items: center;
    align-items: center;
    transition: $transition;
    transform: rotate(0deg);
    cursor: pointer;

    span {
      width: 6px;
      display: inline-block;
      height: 6px;
      border-radius: 50%;
      background-color: $white;
    }
  }
  &:hover,
  &:focus {
    .sidedot-inner {
      transform: rotate(135deg);
    }
  }
}

.bg-white {
  background-color: $white;
}

.bg-grey {
  background-color: #f2f0fe;
}

.video-wrap {
  a {
    @include box(84px);
    background-color: $primary-color;
    font-size: 20px;
    color: $white;
  }
}

.pagination-wrapper {
  padding-top: 50px;
  margin-top: 65px;
  border-top: 1px solid #d9d9d9;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  flex-wrap: wrap;
  a {
    @include box(50px);
    border: 1px solid $tertiary-color;
    background-color: $white;
    color: $tertiary-color;
    transition: $transition;

    &:hover {
      background-color: $tertiary-color;
      color: $white;
      border: 1px solid $tertiary-color;
    }
  }

  .active {
    background-color: $tertiary-color;
    color: $white;
    border: 1px solid $tertiary-color;
  }

  button {
    font-size: 20px;
    &:hover {
      color: $tertiary-color;
    }
  }
}

.registration-popup {
  position: fixed;
  inset: 0px;
  z-index: 999999;
  background-color: $black;
  min-height: 100vh;
  max-height: 100vh;
  max-width: 100vw;
  min-width: 100vw;
  width: 100vw;
  overflow-y: auto;
  transform: scale(0);
  visibility: hidden;
  opacity: 0;
  transition: all 0.8s cubic-bezier(0.215, 0.61, 0.355, 1);

  .close-registration {
    position: absolute;
    top: 60px;
    right: 60px;
    color: #6d6d6d;
    background-color: transparent;
    font-size: 30px;
    &:hover {
      color: $white;
    }
  }

  .thumb {
    height: 100%;
    background-image: url("../../../public/images/registration-thumb.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: right top;
    min-height: 600px;
    position: relative;
    .cta__grop {
      position: absolute;
      top: 50%;
      min-width: 400px;
      transform: translateY(-50%) translateX(170px) rotate(-90deg);
      right: 30px;

      .btn {
        border-radius: 0px;
        width: auto !important;
        margin: 6px;
        text-transform: capitalize;
      }

      .btn--secondary {
        border: 1px solid $white !important;
        color: $white !important;
        &:hover {
          border: 1px solid $tertiary-color !important;
        }
      }
    }
  }
}

.registration-popup-active {
  transform: scale(1);
  visibility: visible;
  opacity: 1;
}

.registration-inner {
  min-width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 100px 20px 60px;

  .single {
    width: 100%;
    height: 100%;
    max-width: 1410px;
    padding: 65px;
    background-image: url("../../../public/images/registration-bg.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: left top;
    overflow-y: auto;
  }
}

.regi-active {
  border: 1px solid $tertiary-color !important;
  background-color: $tertiary-color;
  color: $white;
}

.registration__content {
  height: 100%;
  padding-left: 35px;
  .h2 {
    color: $white;
  }

  form {
    margin-top: 65px;
  }

  .divide {
    margin: 40px 0px;
    text-align: center;
    p {
      color: $white;
      text-transform: uppercase;
      font-weight: 600;
    }
  }

  .group-input {
    border-bottom: 1px solid $white !important;
    margin-bottom: 40px;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
    input {
      color: $white !important;
      width: 100%;
      &::placeholder {
        color: $white !important;
      }
    }
  }

  .authentic {
    display: flex;
    flex-direction: column;
    gap: 15px;
    .btn {
      width: 100%;
      gap: 70px;
      justify-content: flex-start;
      border-radius: 0px;
      background-color: $white;
      border: 1px solid $white !important;
      font-weight: 700;
      color: $theme-color;
      text-transform: capitalize;
      font-size: 16px;
      &::before {
        width: 0px !important;
      }

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .cta__group {
    .btn {
      width: 100%;
      border-radius: 0px;
      text-transform: capitalize;
      &:hover {
        border: 1px solid $white !important;
      }
    }
  }

  .group-radio {
    label {
      color: $white;
    }

    input {
      background-color: $white;
      border: 1px solid $white !important;
    }
  }
}

#createModal {
  .thumb {
    background-image: url("../../../public/images/registration-thumb-two.png");
  }
}

@keyframes smallMove {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1) translateY(0px);
    left: 0px;
  }
  20% {
    opacity: 0.8;
    transform: scale(1.2) translateY(50%);
    left: 40%;
  }
  40% {
    opacity: 0.3;
    transform: scale(0.8) translateY(80%);
    left: 80%;
    bottom: 60%;
  }
  60% {
    opacity: 0.9;
    transform: scale(0.4) translateY(50%);
    left: 90%;
    bottom: 20%;
  }
  80% {
    opacity: 1;
    transform: scale(1.2) translateY(0%);
    left: 20%;
    bottom: 30%;
  }
}

@keyframes smallMoveTwo {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1) translateY(0px);
    right: 0px;
  }
  20% {
    opacity: 0.8;
    transform: scale(1.2) translateY(50%);
    right: 40%;
  }
  40% {
    opacity: 0.3;
    transform: scale(0.8) translateY(80%);
    right: 80%;
    bottom: 60%;
  }
  60% {
    opacity: 0.9;
    transform: scale(0.4) translateY(50%);
    right: 90%;
    bottom: 20%;
  }
  80% {
    opacity: 1;
    transform: scale(1.2) translateY(0%);
    right: 20%;
    bottom: 30%;
  }
}

@keyframes smaller {
  0% {
    opacity: 1;
  }
  40% {
    transform: scale(1.2);
  }
  80% {
    transform: scale(0.7);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes wave {
  0% {
    transform: translate(-50%, -50%) scale(0.6);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    transform: translate(-50%, -50%) scale(1.7);
    opacity: 0;
  }
}

// custom video modal
.video-backdrop {
  position: fixed;
  inset: 0px;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.5s;
  z-index: 99999 !important;
  box-sizing: content-box;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease-out;
}

.video-inner {
  max-width: 100%;
  height: calc(100% - 120px);
  max-height: 100%;
  min-height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 60px 20px;
  overflow-y: auto;
  cursor: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVBAMAAABbObilAAAAMFBMVEVMaXH////////////////////////////////////////////////////////////6w4mEAAAAD3RSTlMAlAX+BKLcA5+b6hJ7foD4ZP1OAAAAkUlEQVR4XkWPoQ3CUBQAL4SktoKAbCUjgAKLJZ2ABYosngTJCHSD6joUI6BZgqSoB/+Shqde7sS9x3OGk81fdO+texMtRVTia+TsQtHEUJLdohJfgNNPJHyEJPZTsWLoxShqsWITazEwqePAn69Sw2TUxk1+euPis3EwaXy8RMHSZBIlRcKKnC5hRctjMf57/wJbBlAIs9k1BAAAAABJRU5ErkJggg==),
    progress;
}

.video-zoom-in {
  visibility: visible;
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.9);
  .video-container {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }
}

.video-container {
  position: relative;
  max-width: 850px;
  max-height: 480px;
  width: 100%;
  height: 100%;
  z-index: 9999999 !important;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease-in-out;
  transform: scale(0.7);
  transition-delay: 0.3s;
}

.video {
  width: 100%;
  height: 100%;
  border: 0px;
}

.close-video-popup {
  position: absolute;
  top: -60px;
  right: -6px;
  background-color: transparent;
  font-size: 30px;
  box-shadow: none;
  border: 0px;
  color: white;
  margin: 0px;
  padding: 0px;
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  &:hover {
    color: white;
  }
}

@media only screen and (max-width: 991.98px) {
  .video-container {
    max-width: 540px;
    max-height: 300px;
  }

  .close-video-popup {
    top: -50px;
    font-size: 24px;
  }
}

/* ==== 
 --------- (2.3) global styles end ---------
 ==== */
