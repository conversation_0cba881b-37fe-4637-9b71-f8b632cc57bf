/* ==== 
 --------- (2.1) reset styles start ---------
 ==== */

* {
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;

  &::-moz-selection {
    color: #ffffff;
    background-color: #1770c8;
  }

  &::selection {
    color: #ffffff;
    background-color: #1770c8;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: $theme-font;
  font-size: 16px;
  line-height: 0px;
  font-weight: 400;
  color: $theme-color;
  background-color: $theme-bg;
  overflow-x: clip;

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background-color: #cae6f7;
    border-radius: 5px;
  }

  &::-webkit-scrollbar-button,
  &::-webkit-scrollbar-thumb {
    background-color: $tertiary-color;
    border-radius: 5px;
  }
}

button {
  background-color: transparent;
  border: 0px;
  outline: 0px;
}

a,
button {
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  outline: 0px;
  border: 0px;
  transition: $transition;
  letter-spacing: 1px;
  cursor: pointer;

  i {
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    transition: none;
  }

  &:hover {
    text-decoration: none;
    border: 0px;
    outline: 0px;
  }
  &:focus {
    box-shadow: none;
  }
}

ul,
ol {
  list-style-type: none;
  list-style-position: inside;
  margin: 0px;
  padding: 0px;
}

hr,
blockquote,
textarea {
  margin: 0px;
}

input,
textarea {
  border: 0px;
  outline: 0px;

  &:focus {
    box-shadow: none;
  }
}

input {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0px;
  }
}

input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type="checkbox"] {
  width: initial;
  height: initial;
}

textarea {
  min-height: 150px;
  resize: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0px;
}

iframe {
  border: 0px;
  width: 100%;
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6,
p {
  font-family: $dm;
  padding: 0px;
  margin: 0px;
  font-weight: 700;
  color: $theme-color;
}

p {
  font-weight: 400;
}

.container {
  padding-left: 15px !important;
  padding-right: 15px !important;
}

.container-fluid {
  padding-left: 15px !important;
  padding-right: 15px !important;
}

.row {
  margin-left: -15px !important;
  margin-right: -15px !important;
}

.row > * {
  padding-left: 15px !important;
  padding-right: 15px !important;
}

/* ==== 
   --------- (2.1) reset styles end ---------
   ==== */
