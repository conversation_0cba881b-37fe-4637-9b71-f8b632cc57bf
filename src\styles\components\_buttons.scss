/* ==== 
 --------- (3.1) buttons styles start ---------
 ==== */

.btn {
  padding: 18px 28px;
  text-transform: uppercase;
  border: 1px solid $tertiary-color !important;
  font-size: 14px;
  line-height: 18px;
  font-weight: 700;
  transition: $transition;
  color: $tertiary-color;
  position: relative;
  border-radius: 5px;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  i {
    transition: none;
  }
  &:hover {
    border: 1px solid $tertiary-color !important;
  }
}

.btn--primary {
  z-index: 1;
  color: $white;
  &::before {
    content: "";
    position: absolute;
    inset: 0px;
    width: 100%;
    height: 100%;
    background-color: $tertiary-color;
    transition: $transition;
    z-index: -1;
    border-radius: 0px;
  }

  &:hover {
    color: $tertiary-color;
    background-color: $white;
    &::before {
      width: 0%;
    }
  }
}

.btn--secondary {
  z-index: 1;
  &::before {
    content: "";
    position: absolute;
    inset: 0px;
    width: 0px;
    height: 100%;
    background-color: $tertiary-color;
    transition: $transition;
    z-index: -1;
    border-radius: 0px;
  }

  &:hover {
    color: $white;
    background-color: $tertiary-color;
    &::before {
      width: 100%;
    }
  }
}

.btn--tertiary {
  z-index: 1;
  color: $white;
  font-weight: 500;
  text-transform: capitalize;
  padding: 12px 20px;
  border-radius: 30px;
  &::before {
    content: "";
    position: absolute;
    inset: 0px;
    width: 100%;
    height: 100%;
    background-color: $tertiary-color;
    transition: $transition;
    z-index: -1;
    border-radius: 0px;
  }

  &:hover {
    color: $tertiary-color;
    background-color: $white;
    &::before {
      width: 0%;
    }
  }
}

.btn--quaternary {
  z-index: 1;
  padding: 12px 20px;
  font-weight: 500;
  text-transform: capitalize;
  border-radius: 30px;
  &::before {
    content: "";
    position: absolute;
    inset: 0px;
    width: 0px;
    height: 100%;
    background-color: $tertiary-color;
    transition: $transition;
    z-index: -1;
    border-radius: 0px;
  }

  &:hover {
    color: $white;
    background-color: $tertiary-color;
    &::before {
      width: 100%;
    }
  }
}

.btn--quinary {
  z-index: 1;
  color: $tertiary-color;
  font-weight: 600;
  text-transform: uppercase;
  padding: 14px 20px;
  border: 0px solid transparent !important;
  border-radius: 5px;
  &::before {
    content: "";
    position: absolute;
    inset: 0px;
    width: 100%;
    height: 100%;
    background-color: #f0efff;
    transition: $transition;
    z-index: -1;
    border-radius: 0px;
  }

  &:hover {
    color: $white;
    background-color: $tertiary-color;
    border: 0px solid transparent !important;
    &::before {
      width: 0%;
    }
  }
}

.btn--senary {
  z-index: 1;
  color: $theme-color;
  font-weight: 600;
  text-transform: capitalize;
  padding: 14px 20px;
  border: 1px solid #dadada !important;
  border-radius: 30px;
  span {
    line-height: 0px;
    color: $primary-color;
    font-weight: 600;
    transition: $transition;
  }
  &::before {
    content: "";
    position: absolute;
    inset: 0px;
    width: 0%;
    height: 100%;
    background-color: $tertiary-color;
    transition: $transition;
    z-index: -1;
    border-radius: 0px;
  }

  &:hover {
    color: $white;
    background-color: $tertiary-color;
    border: 1px solid $tertiary-color !important;
    span {
      color: $white;
    }
    &::before {
      width: 100%;
    }
  }
}

.slide-btn {
  @include box(74px);
  border: 1px solid $white;
  transition: $transition;
  background-color: transparent;
  color: $white;
  font-size: 40px;
  z-index: 2;
  &:hover {
    color: $tertiary-color;
    background-color: $white;
    border: 1px solid $white;
  }
}

// scroll to top with progress

.progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  background-color: $white;
  box-shadow: inset 0 0 0 8px #c3b4ba7d;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  transition: all 200ms linear;
  z-index: 99;
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.progress-wrap::after {
  position: absolute;
  content: "\f102";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  text-align: center;
  line-height: 50px;
  font-size: 16px;
  color: $primary-color;
  left: 0%;
  top: 0%;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}

.progress-wrap:hover::after {
  opacity: 0;
}

.progress-wrap::before {
  position: absolute;
  content: "\f102";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  text-align: center;
  line-height: 50px;
  font-size: 16px;
  opacity: 0;
  background: $primary-color;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  left: 0%;
  top: 0%;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 2;
  transition: all 200ms linear;
}

.progress-wrap:hover::before {
  opacity: 1;
}

.progress-wrap svg path {
  fill: none;
}

.progress-wrap svg.progress-circle path {
  stroke: $primary-color;
  stroke-width: 4;
  box-sizing: content-box;
  transition: all 200ms linear;
}

/* ==== 
 --------- (3.1) buttons styles end ---------
 ==== */
