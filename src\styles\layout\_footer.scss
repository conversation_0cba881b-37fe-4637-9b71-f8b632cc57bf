/* ==== 
 --------- (4.3) footer styles start ---------
 ==== */

.footer {
  background-color: #0c0c0c;
  background-image: url("../../../public/images/footer-bg.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: left bottom;
  padding-bottom: 0px !important;
  overflow-x: clip;

  .gaper {
    row-gap: 60px;
  }

  .content {
    margin: 20px 0px 30px;
    p {
      text-transform: capitalize;
      color: #818181;
    }
  }

  .social {
    justify-content: flex-start;
    gap: 10px;
    a {
      border: 1px solid #414141;
      background-color: transparent;
      color: $white;
      font-size: 16px;
      &:hover {
        background-color: $white;
        color: $tertiary-color;
        border: 1px solid $white;
      }
    }
  }

  .footer__head {
    margin-bottom: 36px;
    .h5 {
      font-weight: 700;
      color: $white;
      text-transform: capitalize;
    }
  }

  .footer__list {
    li {
      margin-bottom: 16px;
      &:nth-last-of-type(1) {
        margin-bottom: 0px;
      }
    }
    a {
      font-size: 16px;
      text-transform: capitalize;
      color: #818181;
      &:hover {
        color: $white;
        padding-left: 8px;
      }
    }
  }

  .footer__address {
    li {
      margin-bottom: 16px;
      &:nth-last-of-type(1) {
        margin-bottom: 0px;
      }
    }
    a {
      display: flex;
      gap: 10px;
      font-size: 16px;
      line-height: 26px;
      text-transform: capitalize;
      color: #818181;
      i {
        color: $tertiary-color;
        transition: inherit;
      }

      &:hover {
        color: $white;
        i {
          color: $white;
        }
      }
    }
  }

  hr {
    height: 4px;
    margin-top: 55px;
    background-color: $primary-color;
    background-image: linear-gradient(
      90deg,
      #4569e7 0%,
      #e74545 36.98%,
      #ffe200 68.75%,
      #e412f1 100%
    );
    opacity: 1;
  }

  .footer__bottom {
    padding: 40px 0px;
  }

  .footer__nav {
    ul {
      display: flex;
      align-items: center;
      column-gap: 24px;
      flex-wrap: wrap;
      row-gap: 10px;
      a {
        text-transform: capitalize;
        color: #818181;
        &:hover {
          color: $white;
        }
      }
    }
  }

  .footer__copy {
    p {
      text-transform: capitalize;
      color: #818181;
    }
    a {
      &:hover {
        color: $white;
      }
    }
  }
}

.bt-two {
  margin-top: 168px !important;
}

.ft-two {
  padding-bottom: 168px !important;
}

// footer two
.footer-two__group {
  background-color: $primary-color;
  padding-left: 34px;
  margin-top: 65px;
  position: relative;
  z-index: 1;
  &::before {
    content: "";
    position: absolute;
    top: 0px;
    left: 100%;
    width: 100%;
    height: 100%;
    background-color: $primary-color;
    min-width: 100vw;
    z-index: -1;
  }
}

.footer-two__group-second {
  display: flex;
  align-items: center;
  column-gap: 20px;
  row-gap: 24px;
  justify-content: space-between;

  p {
    color: $white;
    text-transform: capitalize;
  }

  .h5 {
    margin-top: 4px;
    font-weight: 700;
  }
}

.footer-two__group-social {
  .social {
    justify-content: flex-end;
  }
  a {
    padding: 54px 28px;
    font-size: 24px;
    border-radius: 0px;
    border: 0px !important;
    i {
      font-size: 24px;
      color: $white;
      transition: $transition;
    }
    &:hover {
      background-color: $white;
      color: $primary-color;
      i {
        color: $primary-color;
      }
    }
  }
}

/* ==== 
 --------- (4.3) footer styles end ---------
 ==== */
