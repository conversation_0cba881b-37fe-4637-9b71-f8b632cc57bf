/* ==== 
 --------- (4.1) header styles start ---------
 ==== */

// header one
.header {
  padding: 0px 0px;
  background-color: transparent;
  z-index: 999;
  position: relative;

  .nav__logo {
    display: flex;
    align-items: center;
    column-gap: 120px;
  }

  .nav__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    min-width: 100%;
  }

  .nav__menu {
    flex-grow: 1;
  }

  .nav__menu-items {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .nav__menu-link {
    padding: 41px 22px;
    color: $theme-color;
    font-weight: 600;
    text-transform: uppercase;

    &:hover {
      color: $tertiary-color;
    }
  }

  .nav__menu-item--dropdown {
    position: relative;

    .nav__dropdown {
      position: absolute;
      top: 100%;
      left: 0px;
      min-width: 220px;
      max-width: 300px;
      background-color: $primary-color;
      padding: 30px;
      opacity: 0;
      visibility: hidden;
      transition: $transition;
      transform: translateY(30px);
      pointer-events: none;
      background: linear-gradient(180deg, #ffffff 0%, #fffdf0 100%);
      box-shadow: 0px 4px 60px rgba(69, 105, 231, 0.15);
      border-radius: 0px 0px 10px 10px;
      border: 0px solid transparent;
      border-top: 3px solid #4569e7;
      z-index: 99;

      &::-webkit-scrollbar {
        width: 0px;
      }

      &::before,
      &::after {
        content: "";
        display: table;
      }

      &::after {
        clear: both;
      }

      li {
        margin-bottom: 10px;
        &:nth-last-of-type(1) {
          margin-bottom: 0px;
        }
      }
    }

    .mega-menu {
      transform: translateY(30px) translateX(-49%);
      min-width: 1410px !important;
      background: linear-gradient(180deg, #ffffff 0%, #fffdf0 100%);
      box-shadow: 0px 4px 60px rgba(69, 105, 231, 0.15);
      border-radius: 0px 0px 10px 10px;
      border: 0px solid transparent;
      border-top: 3px solid #4569e7;
      padding: 40px;
    }

    &:hover {
      .nav__dropdown {
        opacity: 1;
        visibility: visible;
        transform: translateY(0px);
        pointer-events: auto;
      }

      .mega-menu {
        transform: translateY(0px) translateX(-49%);
      }
    }

    .nav__dropdown-item {
      font-size: 14px;
      font-weight: 600;
      text-transform: uppercase;
      width: 100%;
      color: $theme-color;

      &:hover {
        padding-left: 8px;
        color: $tertiary-color;
        border-color: $tertiary-color;
      }
    }
  }

  .nav__dropdown--alt {
    max-width: 600px !important;
    min-width: 400px !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0px;
    width: 100%;
    ul {
      width: calc(50% - 12px);
      &:nth-of-type(1) {
        margin-right: 24px;
      }
    }
  }

  .mega-menu__inner {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    column-gap: 100px;
  }

  .mega-menu__single--alt {
    flex-grow: 1;
    max-width: 500px;
  }

  .mega-menu__single {
    .h5 {
      font-size: 22px;
      font-weight: 500;
      text-transform: capitalize;
      margin-left: 10px;
    }
  }

  .mega-menu__single-head {
    margin-bottom: 30px;
  }

  .mega-menu__single-item {
    margin-bottom: 15px;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
    a {
      width: 100%;
      padding: 10px 24px 10px 10px;
      border-radius: 10px;
      background-color: transparent;
      letter-spacing: 0px;
      display: flex;
      align-items: center;
      gap: 10px;
      position: relative;
      z-index: 1;
      &::before {
        content: "";
        position: absolute;
        inset: 0px;
        width: 0%;
        height: 100%;
        transition: $transition;
        background: linear-gradient(89.83deg, #e9eeff -1.72%, #c4d1ff 103.86%);
        border-radius: 10px;
        z-index: -1;
      }

      img {
        width: 90px;
        height: 44px;
        border-radius: 5px;
      }

      span {
        font-size: 18px;
        text-transform: capitalize;
        font-weight: 600;
        color: $theme-color;
      }

      &:hover {
        box-shadow: 0px 4px 60px -7px rgba(69, 105, 231, 0.1);
        &::before {
          width: 100%;
        }
      }
    }
  }

  .mega-menu__single-img {
    a {
      img {
        width: 48px;
        height: 48px;
      }
    }
  }

  .nav__menu-link--dropdown {
    position: relative;

    &::after {
      font-family: "Font Awesome 6 Free";
      font-weight: 900;
      content: "\f107";
      border: none;
      font-size: 16px;
      transition: all 0.2s ease-in-out;
    }
  }

  .nav__menu-item--dropdown {
    &:hover {
      .nav__menu-link--dropdown {
        color: $tertiary-color;

        &::after {
          transform: rotate(180deg);
          color: $tertiary-color;
        }
      }
    }

    .nav__menu-link-child {
      position: relative;
      a {
        color: $tertiary-color !important;
        justify-content: space-between !important;

        &::after {
          color: $tertiary-color !important;
          transform: rotate(0deg) !important;
          right: 0px;
        }

        &:hover {
          color: $tertiary-color !important;
          &::after {
            transform: rotate(-90deg) !important;
          }
        }
      }
      &:hover {
        .nav__dropdown-child {
          opacity: 1;
          visibility: visible;
          transform: translateY(0px);
        }
      }
    }

    .nav__dropdown-child {
      padding: 20px 30px;
      background: $primary-color;
      border-radius: 5px;
      opacity: 0;
      visibility: hidden;
      transform: translateY(30px);
      position: absolute;
      top: 0px;
      left: 100%;
      min-width: 170px;
      transition: $transition;
      box-shadow: $shadow;
    }
  }

  .btn {
    padding: 14px 20px;
    background: #f0efff;
    border-radius: 5px;
    color: #2b1b9a;
    border: 0px solid transparent !important;
    &:hover {
      color: $white;
    }
  }

  .social {
    margin-top: 40px;
    display: none;
    a {
      &:hover {
        color: $white;
      }
    }
  }

  .nav__uncollapsed {
    margin-left: 40px;
    display: flex;
    align-items: center;
    gap: 30px;
  }

  .nav__uncollapsed-item {
    display: flex;
    align-items: center;
    gap: 24px;

    .cmn-button--secondary {
      &:hover {
        color: $white;
        border: 1px solid $primary-color;
      }
    }

    .cmn-button {
      &:nth-last-of-type(1) {
        border: 1px solid $primary-color;
        &:hover {
          border: 1px solid #cecece;
          background-color: transparent;
        }
      }
    }
  }

  .nav__bar {
    padding: 0px;
    display: inline-block;
    background-color: transparent;

    span {
      transform: translateY(0px);
    }

    .icon-bar {
      width: 35px;
      height: 2px;
      background-color: $tertiary-color;
      margin: 0px;
      display: block;
      transition: all 0.3s;
    }

    .middle-bar {
      margin: 6px 0px;
      opacity: 1;
    }
  }

  .nav__bar-toggle {
    .top-bar {
      transform: rotate(-45deg) translate(-7px, 4px);
      background: $primary-color;
    }

    .middle-bar {
      opacity: 0;
    }

    .bottom-bar {
      transform: rotate(45deg) translate(-7px, -4px);
      background: $primary-color;
    }
  }
}

.header-active {
  background-color: $white;
  animation: header 0.5s linear;
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  width: 100%;
  z-index: 99;
  box-shadow: $shadow;

  .nav {
    box-shadow: none !important;
  }
}

.backdrop {
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  width: 100vw;
  height: 100vh;
  background-color: #12121260;
  z-index: 9;
  transform: translateX(-100%);
  transition: $transition;
  display: none;
  cursor: pointer;
}

.backdrop-active {
  transform: translateX(0px);
}

.body-active {
  overflow: hidden;
  min-width: 100vw;
  min-height: 100vh;
  max-height: 100vh;
}

@keyframes header {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0px);
  }
}

// off canvas

.off-canvas {
  position: fixed;
  left: 0px;
  top: 0px;
  bottom: 0px;
  width: 100%;
  max-width: 320px;
  height: 100vh;
  min-height: 100vh;
  background-color: $white;
  z-index: 9999;
  box-shadow: $shadow;
  border-right: 5px solid $primary-color;
  transition: $transition;
  transform: translateX(-100%);

  .off-canvas__inner {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    max-width: 320px;
    padding: 40px 20px;
    display: flex;
    align-items: center;
    gap: 40px;
    flex-direction: column;
    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-track {
      border-radius: 5px;
    }

    &::-webkit-scrollbar-button,
    &::-webkit-scrollbar-thumb {
      border-radius: 5px;
    }
  }

  .off-canvas__head {
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: space-between;
    width: 100%;

    button {
      background-color: transparent;
      font-size: 24px;
      color: $primary-color;
    }
  }

  .offcanvas__search {
    width: 100%;
    form {
      display: flex;
      align-items: center;
      gap: 24px;
      justify-content: space-between;
      border-bottom: 1px solid $theme-color;

      input {
        flex-grow: 1;
        padding: 10px 0px;
        color: $theme-color;
        &::placeholder {
          font-weight: 400;
          color: $theme-color;
          font-size: 16px;
        }
      }

      button {
        background-color: transparent;
        font-size: 20px;
        color: $theme-color;
        &:hover {
          color: $tertiary-color;
        }
      }
    }
  }

  .off-canvas__contact {
    width: 100%;
    text-align: start;

    h4 {
      color: $theme-color;
      text-transform: uppercase;
      font-weight: 700;
      margin-bottom: 25px;
    }

    .single {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 24px;
      span {
        @include box(40px);
        border: 1px solid $tertiary-color;
        color: $tertiary-color;
      }

      a {
        color: $theme-color;
        font-weight: 600;
        font-size: 16px;
        &:hover {
          color: $tertiary-color;
        }
      }
    }
  }

  .social-side {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: flex-start;
    gap: 16px;
    a {
      @include box(40px);
      background-color: #f0efff;
      color: $tertiary-color;
      &:hover {
        background-color: $tertiary-color;
        color: $white;
      }
    }
  }
}

.off-canvas-active {
  transform: translateX(0px);
}

.off-canvas-backdrop {
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  width: 100%;
  height: 100%;
  background-color: rgba(22, 22, 22, 0.5921568627);
  z-index: 999;
  transform: translateX(-100%);
  transition: $transition;
}

.off-canvas-backdrop-active {
  transform: translateX(0px);
  cursor: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVBAMAAABbObilAAAAMFBMVEVMaXH////////////////////////////////////////////////////////////6w4mEAAAAD3RSTlMAlAX+BKLcA5+b6hJ7foD4ZP1OAAAAkUlEQVR4XkWPoQ3CUBQAL4SktoKAbCUjgAKLJZ2ABYosngTJCHSD6joUI6BZgqSoB/+Shqde7sS9x3OGk81fdO+texMtRVTia+TsQtHEUJLdohJfgNNPJHyEJPZTsWLoxShqsWITazEwqePAn69Sw2TUxk1+euPis3EwaXy8RMHSZBIlRcKKnC5hRctjMf57/wJbBlAIs9k1BAAAAABJRU5ErkJggg==),
    progress;
}

// header two
.header-two {
  background-color: transparent;
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;

  .nav {
    padding: 30px 0px;
  }

  .nav__menu {
    position: fixed;
    top: 0px;
    left: 0px;
    bottom: 0px;
    min-width: 400px;
    max-width: 400px;
    height: 100vh;
    z-index: 99;
    overflow-x: clip;
    overflow-y: auto;
    padding: 40px 20px;
    background: $white;
    box-shadow: $shadow;
    transform: translateX(-110%);
    transition: 0.3s ease-in-out;

    &::-webkit-scrollbar {
      width: 0px;
    }
  }

  .nav__logo {
    column-gap: 30px;
  }

  .nav__menu-logo {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    gap: 20px;

    img {
      max-width: 130px;
    }

    .nav__menu-close {
      font-size: 30px;
      color: $theme-color;
    }
  }

  .nav__menu-active {
    transform: translateX(0px);
  }

  .nav__menu-items {
    flex-direction: column;
    align-items: flex-start;
  }

  .nav__menu-item {
    width: 100%;
    margin-bottom: 12px;

    &:nth-last-of-type(1) {
      margin-bottom: 0px;
      margin-top: 0px;

      .btn {
        width: 100%;
        justify-content: center;
        color: $white;
        padding: 20px;
        &:hover {
          background-color: $theme-color;
        }
      }
    }
  }

  .nav__menu-item--dropdown {
    .nav__dropdown,
    .nav__dropdown-child {
      position: static;
      transform: translateY(0px);
      padding: 20px;
      margin-top: 5px;
      display: none;
      opacity: 1;
      visibility: visible;
      min-width: 100%;
      background-color: rgba(12, 169, 64, 0.4);
      box-shadow: $shadow;

      li {
        margin-bottom: 14px;
      }
    }

    .nav__dropdown-item {
      color: #3b3b3b !important;
    }

    .nav__dropdown-active {
      display: block !important;
      animation: atg 0.4s ease-in-out;
    }

    @keyframes atg {
      0% {
        transform: translateY(-10px);
      }

      100% {
        transform: translateY(0px);
      }
    }

    .nav__menu-link--dropdown {
      &:hover {
        color: $tertiary-color !important;

        &::after {
          transform: rotate(0deg);
          color: $tertiary-color !important;
        }
      }
    }

    .nav__menu-link--dropdown-active {
      color: $tertiary-color !important;

      &::after {
        transform: rotate(180deg) !important;
        color: $tertiary-color !important;
      }
    }

    .nav__menu-link-child {
      a {
        color: $theme-color !important;
        &:hover {
          color: $theme-color !important;
          &::after {
            color: $theme-color !important;
            transform: rotate(-90deg) !important;
          }
        }
        &::after {
          color: $theme-color !important;
          transform: rotate(-90deg) !important;
        }
      }
    }

    .nav__dropdown-child {
      left: 0px;
      top: 100%;
      right: 0px;
      width: 100%;
    }
  }

  .nav__menu-link {
    width: 100%;
    background-color: #f0efff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-radius: 5px;
  }

  .backdrop {
    display: block;
  }

  .nav__uncollapsed {
    margin-left: 0px;
  }

  .nav__dropdown--alt {
    max-width: 100% !important;
    min-width: 100% !important;
    flex-direction: column;
    gap: 12px;
    ul {
      width: 100%;
      margin: 0px;

      &:nth-of-type(1) {
        margin-bottom: 12px;
      }
    }
  }

  .nav__menu-item--dropdown {
    .mega-menu {
      min-width: 100% !important;
      max-width: 100%;
      transform: translate(0px, 0px);
      padding: 40px 20px;
    }

    &:hover {
      .mega-menu {
        transform: translate(0px, 0px);
      }
    }
  }

  .mega-menu__inner {
    flex-direction: column;
    align-items: flex-start;
    gap: 60px;
  }

  .mega-menu__single--alt {
    max-width: 400px;
    width: 100%;
  }

  .mega-menu__single-item {
    a {
      img {
        max-width: 70px;
      }

      span {
        font-size: 16px;
      }
    }
  }

  .mega-menu__single-head {
    margin-bottom: 20px;
    .h5 {
      font-size: 20px;
    }
  }
}

.header-two-active {
  background-color: #f0efff;
  .nav {
    padding: 20px 0px;
  }
}

.header-three {
  background-image: linear-gradient(
    90deg,
    #4569e7 0%,
    #e74545 38.02%,
    #ff880a 73.44%,
    #ff0093 100%
  );

  .nav__content {
    gap: 16px;
  }

  .nav__menu-link {
    color: $white;
    padding: 41px 18px;
    &:hover {
      color: $white;
    }
  }

  .nav__menu-link--dropdown {
    color: $white;
    &::after {
      color: $white;
    }

    &:hover {
      color: $white;
      &::after {
        color: $white;
      }
    }
  }

  .nav__menu-item--dropdown {
    &:hover {
      .nav__menu-link--dropdown {
        color: $white;

        &::after {
          color: $white;
        }
      }
    }
  }

  .btn--secondary {
    i {
      transform: rotate(50deg);
    }
  }

  .nav__bar {
    .icon-bar {
      background-color: $white;
    }
  }

  .currency {
    padding-left: 0px;
    background-color: transparent;
    border: 0px solid transparent;
    .current {
      text-transform: uppercase;
      font-size: 14px;
      color: $white;
      font-weight: 700;
    }

    &::after {
      width: 10px;
      height: 10px;
      margin-top: -8px;
      border-color: $white;
    }
  }

  @media only screen and (max-width: 1439.98px) {
    .nav__menu-link {
      padding: 41px 14px !important;
    }

    .nav__logo {
      column-gap: 80px;
    }
  }

  @media only screen and (max-width: 1399.98px) {
    .nav__menu-link {
      padding: 31px 8px !important;
    }

    .nav__uncollapsed {
      margin-left: 0px;
    }

    .nav__logo {
      column-gap: 24px !important;
    }
  }

  @media only screen and (max-width: 1199.98px) {
    .nav__menu-link {
      color: $theme-color;
      padding: 16px !important;

      &:hover {
        color: $tertiary-color;
      }
    }

    .nav__logo {
      column-gap: 30px !important;
    }

    .nav__menu-link--dropdown {
      color: $theme-color;

      &::after {
        color: $theme-color;
      }
    }

    .nav__menu-item--dropdown {
      &:hover {
        .nav__menu-link--dropdown {
          color: $tertiary-color;

          &::after {
            color: $tertiary-color;
          }
        }
      }
    }
  }
}

.header-three-active {
  background-image: linear-gradient(
    90deg,
    #4569e7 0%,
    #e74545 38.02%,
    #ff880a 73.44%,
    #ff0093 100%
  );
}

.cmn-header {
  .nav__menu-link {
    padding: 51px 22px;
  }

  .sidedot {
    span {
      background-color: $primary-color;
    }
  }
}

/* ==== 
 --------- (4.1) header styles end ---------
 ==== */
