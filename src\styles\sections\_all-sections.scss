/* ====
 --------- (5.0) all sections styles start ---------
 ==== */

//  service
.services {
  overflow-x: clip;
  .services__slider-single {
    height: 450px;
    border-radius: 10px;
    background-color: #e74545;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    img {
      transition: $transition;
    }

    &:hover {
      img {
        opacity: 0.25;
      }
      .services__slider-single__content {
        transform: scale(1);
        opacity: 1;
        visibility: visible;
      }
    }
  }

  .on {
    background-color: #ffcd1e;
  }

  .tw {
    background-color: #67b6f4;
  }

  .th {
    background-color: #4569e7;
  }

  .fo {
    background-color: #ff6c26;
  }

  .fi {
    background-color: #e74545;
  }

  .services__slider-single__content {
    position: absolute;
    inset: 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    gap: 24px;
    padding: 65px 24px;
    z-index: 2;
    transform: scale(0);
    opacity: 0;
    visibility: hidden;
    transition: $transition;

    .h4 {
      color: $white;
      font-weight: 700;
      text-transform: capitalize;
    }

    a {
      @include box(84px);
      background-color: $white;
      color: $tertiary-color;
      font-size: 36px;
      outline: 1px solid $white;
      outline-offset: 12px;
    }

    .price-tag {
      padding: 10px 20px;
      background-image: url("../../../public/images/services/frame.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;

      p {
        text-transform: uppercase;
        font-size: 14px;
        line-height: 20px;
        font-weight: 700;
        color: $white;
        span {
          color: #e74545;
        }
      }
    }
  }

  .swiper-slide-active {
    img {
      opacity: 0.25;
    }
    .services__slider-single__content {
      transform: scale(1);
      opacity: 1;
      visibility: visible;
    }
  }
}

// about section
.about-section {
  overflow-x: clip;
  position: relative;
  z-index: 1;

  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 13%;
    left: 25%;
    width: 74px;
    height: 74px;
    border-radius: 50%;
    background-color: $tertiary-color;
    animation: smallMove 60s infinite linear;
  }
  &::before {
    width: 30px;
    height: 30px;
    background-color: #ff880a;
    top: 50%;
    left: 15%;
    animation: smallMoveTwo 30s infinite linear;
    animation-direction: alternate-reverse;
  }

  .h2 {
    max-width: 560px;
  }

  .paragraph {
    max-width: 600px;
  }

  .about-section__thumb {
    position: relative;
    text-align: end;

    img {
      border-radius: 10px;
    }

    .about-section__thumb-content {
      @include box(170px);
      background-color: #e74545;
      padding: 20px;
      position: absolute;
      top: 25px;
      left: 0px;
      .h5 {
        color: $white;
        text-align: center;
        span {
          font-size: 14px;
        }
      }
    }
  }

  .about-section__content {
    padding-left: 30px;
  }
}

// work section
.work-section {
  .work-section__inner {
    padding: 65px;
    padding-bottom: 0px !important;
    background-image: linear-gradient(
      180deg,
      #ffeded 0%,
      rgba(242, 240, 254, 0) 100%
    );
    border-radius: 10px;
  }

  .section__header {
    margin-bottom: 50px;
    .h6 {
      margin-bottom: 10px;
    }
  }

  li {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 44px;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
    .thumb {
      @include box(60px);
      background-color: $white;

      span {
        @include box(48px);
        background-color: #ffe0e0;
        font-weight: 700;
        text-transform: uppercase;
        color: #e74545;
      }
    }

    p {
      max-width: 400px;
    }

    &:nth-of-type(2) {
      .thumb {
        span {
          background-color: #f0f8fe;
          color: #4569e7;
        }
      }
    }

    &:nth-of-type(3) {
      .thumb {
        span {
          background-color: #fff7e8;
          color: #ffcd1e;
        }
      }
    }
  }
}

// choose section
.choose-section {
  overflow-x: clip;
  background-color: $theme-color;
  background-image: url("../../../public/images/choose/choose-bg.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: left top;

  .title {
    color: $white;
  }

  ul {
    margin-top: 40px;
  }

  li {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 40px;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
  }

  .thumb {
    width: 64px;
    height: 64px;
    min-width: 64px;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .content {
    p {
      color: $white;
      text-transform: capitalize;
    }
  }

  .h5 {
    font-weight: 700;
    margin-bottom: 12px;
  }

  .cta__group {
    justify-content: flex-start;
    margin-bottom: -20px;
    a {
      font-weight: 700;
      text-transform: uppercase;
      display: flex;
      align-items: center;
      gap: 12px;
      color: $white;
      i {
        font-size: 60px;
      }

      &:hover {
        color: $primary-color;
        padding-left: 12px;
      }
    }
  }

  .cbv {
    position: relative;
    z-index: 1;
  }

  .choose-section__thumb {
    position: relative;
    z-index: 1;
  }

  .choose-section__thumb-main {
    position: relative;
    width: 100%;
    max-width: 600px;
    border-radius: 10px;
    overflow: hidden;

    img {
      width: 100%;
      height: auto;
      border-radius: 10px;
      object-fit: cover;
    }
  }

  .video-wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 99;
    a {
      position: relative;
      z-index: 99;
    }
  }
}

.video-wrap {
  a {
    position: relative;
    z-index: 1;
    &::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: calc(100% + 20px);
      height: calc(100% + 20px);
      border-radius: 50%;
      background-color: inherit;
      opacity: 0.8;
      z-index: -1;
      animation: wave 3s linear infinite;
    }
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: calc(100% + 40px);
      height: calc(100% + 40px);
      border-radius: 50%;
      background-color: inherit;
      opacity: 0.4;
      z-index: -2;
      animation: wave 6s linear infinite;
      animation-delay: 1s;
    }
  }
}

// quality section
.quality-section {
  position: relative;
  overflow-x: clip;
  z-index: 1;
  &::before {
    content: "";
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #c4ceff;
  }
  .title {
    text-transform: capitalize;
  }

  .section__header {
    margin-bottom: 40px;
  }

  .quality-section__filter-btns {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .quality-filter-btn {
    text-transform: capitalize;
    font-size: 16px;
    color: $theme-color;
    gap: 10px;
    padding: 8px 14px;
    border-radius: 30px;
    font-weight: 700;
    &:hover {
      background-color: $tertiary-color;
      color: $white;
    }
  }

  .quality-filter-btn--active {
    background-color: $tertiary-color;
    color: $white;

    i {
      &::before {
        font-weight: 900;
      }
    }
  }

  .quality-section__tab {
    margin-top: 60px;
    img {
      width: 100%;
      border-radius: 10px;
    }
  }

  .quality-section__thumbs {
    img {
      position: absolute;
      z-index: -1;
    }

    .one {
      top: 60%;
      left: 4vw;
      max-width: 15vw;
      min-width: 60px;
      animation: smallMoveTwo 60s infinite linear;
    }

    .two {
      max-width: 15vw;
      min-width: 60px;
      top: 50%;
      transform: translateY(-50%);
      right: 2vw;
      animation: smallMove 60s infinite linear;
      animation-direction: reverse;
    }
  }
}

// testimonial
.testimonial {
  background-color: $primary-color;
  overflow-x: clip;
  position: relative;
  z-index: 1;
  transition: $transition;
  p {
    color: $white;
    max-width: 400px;
  }
  .title {
    max-width: 360px;
    color: $white;
  }

  .testimonial__content-cta {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .testimonial__slider-item {
    padding: 65px 30px;
    background-color: $white;
    border-radius: 10px;
    width: 100%;
    min-height: 450px; /* Minimum height for all testimonial cards */
    height: 100%; /* Take full height of the container */
    display: flex;
    flex-direction: column;
    transition: $transition;

    .quote {
      margin-bottom: 50px;
      i {
        font-size: 40px;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        background-image: linear-gradient(180deg, #652de8 0%, #67b6f4 100%);
      }
    }

    .h4 {
      font-weight: 400;
    }

    hr {
      margin: 60px 0px;
      opacity: 1;
      border: 0px;
      height: 1px;
      width: 100%;
      background-color: #d9d9d9;
    }

    .item__meta {
      display: flex;
      align-items: center;
      gap: 26px;
      row-gap: 16px;
      flex-wrap: wrap;
    }

    .meta__thumb {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    p {
      color: #818181;
    }

    .h5 {
      color: $theme-color;
      text-transform: capitalize;
    }
  }

  .testimonial__slider {
    min-width: 100vw;
  }

  .slick-current + .slick-active + .slick-active {
    opacity: 25%;
    transition: $transition;
  }

  .one {
    position: absolute;
    left: 100px;
    bottom: 105px;
    max-width: 14vw;
    min-width: 70px;
    z-index: -1;
  }
}

// pricing section
.pricing-section {
  background-color: #f8f6fa;
  position: relative;
  overflow-x: clip;

  .pricing-section__inner-item {
    padding: 24px 30px 24px 24px;
    background-color: $white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 24px;
    justify-content: space-between;
    transition: $transition;
    margin-bottom: 20px;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
    &:nth-of-type(2) {
      .h4 {
        color: $theme-color;
      }

      .thumb {
        background-color: $theme-color;
      }
    }
    &:nth-of-type(3) {
      .h4 {
        color: #652de8;
      }

      .thumb {
        background-color: #652de8;
      }
    }
    &:nth-of-type(4) {
      .h4 {
        color: #ff6c26;
      }

      .thumb {
        background-color: #ff6c26;
      }
    }
    &:hover {
      background-color: $tertiary-color;

      .pricing__meta {
        .h4,
        p {
          color: $white;
        }
      }

      .price-plan {
        .h6,
        span,
        p {
          color: $white;
        }
      }

      .thumb {
        background-color: $white;
      }
    }
  }

  .pricing__meta {
    display: flex;
    align-items: center;
    gap: 30px;
    .thumb {
      @include box(114px);
      background-color: #e74545;
      transition: $transition;
    }

    p {
      transition: $transition;
    }

    .h4 {
      font-weight: 700;
      color: $primary-color;
      margin-bottom: 10px;
      text-transform: capitalize;
      transition: $transition;
    }
  }
  .price-frame {
    padding: 12px 24px;
    background-image: url("../../../public/images/pricing/frame.png");
    background-repeat: no-repeat;
    background-size: contain;
    background-position: left bottom;
    .h6 {
      font-weight: 700;
      color: $primary-color;
      text-transform: uppercase;
      line-height: 1;
    }
    p {
      &:nth-last-of-type(1) {
        font-size: 14px;
        color: $white;
        line-height: 1;
        text-transform: capitalize;
        margin-top: 4px;
      }
    }
  }

  .price-plan {
    .h6 {
      font-weight: 700;
      color: $primary-color;
      text-transform: capitalize;
      transition: $transition;
      span {
        color: $theme-color;
        transition: $transition;
      }
    }
    p {
      &:nth-last-of-type(1) {
        font-size: 14px;
        color: #818181;
        line-height: 1;
        text-transform: capitalize;
        margin-top: 8px;
        transition: $transition;
      }
    }
  }
}

// news
.news-section {
  .news__slider-item {
    border: 1px solid #6f18f220;
    border-radius: 10px;
    &:hover {
      .h4 {
        a {
          color: $tertiary-color;
        }
      }

      .tag {
        a {
          background-color: $tertiary-color;
          color: $white;
        }
      }

      .cta {
        a {
          background-color: $tertiary-color;
          color: $white;
        }
      }
    }
  }

  .thumb {
    position: relative;
    a {
      width: 100%;
      border-radius: 10px 10px 0px 0px;
      img {
        width: 100%;
        border-radius: 10px 10px 0px 0px;
      }
    }

    .publish-date {
      position: absolute;
      top: 30px;
      right: 30px;
      text-align: center;
      padding: 12px 16px;
      background-color: $white;

      .h4 {
        font-weight: 700;
        line-height: 1;
        margin-bottom: 6px;
      }

      p {
        font-size: 14px;
        line-height: 1;
        color: $tertiary-color;
      }
    }
  }

  .tag {
    margin-bottom: 20px;
    a {
      font-size: 14px;
      line-height: 1;
      padding: 8px 14px;
      text-transform: capitalize;

      color: #808080;
      border: 1px solid rgba(0, 0, 0, 0.2);
      border-radius: 50px;
      &:hover {
        background-color: $tertiary-color;
        color: $white;
        border: 1px solid $tertiary-color;
      }
    }
  }

  .h4 {
    a {
      font-weight: 600;
      text-transform: capitalize;
      letter-spacing: 0px;
      &:hover {
        color: $tertiary-color;
      }
    }
  }

  .content {
    padding: 36px 30px;
  }

  .cta {
    margin-top: 30px;
    a {
      font-size: 18px;
      @include box(48px);
      border: 1px solid rgba(0, 0, 0, 0.2);
      &:hover {
        background-color: $tertiary-color;
        color: $white;
      }
    }
  }

  .swiper-slide-active {
    .h4 {
      a {
        color: $tertiary-color;
      }
    }

    .tag {
      a {
        background-color: $tertiary-color;
        color: $white;
      }
    }

    .cta {
      a {
        background-color: $tertiary-color;
        color: $white;
      }
    }
  }
}

// cta section
.try-cta {
  .try-cta__inner {
    padding: 65px 40px;
    background: linear-gradient(96.38deg, #4569e7 0.21%, #b81aef 99.54%);
    box-shadow: 0px 4px 100px rgba(43, 27, 154, 0.4);
    border-radius: 10px;
    overflow: hidden;
  }
  .try-cta__thumb {
    position: relative;
    z-index: 1;
    &::before,
    &::after {
      content: "";
      position: absolute;
      width: 240px;
      height: 240px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      z-index: -1;
      right: 0px;
      bottom: -80px;
    }

    &::before {
      width: 40px;
      height: 40px;
      bottom: 60%;
    }
  }
  .paragraph {
    max-width: 430px;
    p {
      color: $white;
    }
  }

  .sub-title,
  .title {
    color: $white;
  }

  .cta__group {
    position: relative;
    justify-content: flex-start;
    display: inline-flex;
    input {
      position: absolute;
      inset: 0px;
      z-index: 9;
      opacity: 0;
      cursor: pointer !important;
    }

    [type="file"] {
      cursor: pointer !important;
      filter: alpha(opacity=0);
      min-width: 100%;
      min-height: 100%;
      visibility: hidden;
      text-indent: -999px;
    }

    .btn {
      background: #f0efff;
      border-radius: 5px;
      position: relative;
    }
  }
}

// sponsor
.sponsor {
  .sponsor__slider-item {
    text-align: center;
    img {
      max-width: 220px;
      opacity: 0.15;
      transition: $transition;
    }
  }

  .swiper-slide-active {
    img {
      opacity: 1;
    }
  }
}

// counter
.counter {
  .counter__inner {
    padding: 65px 60px;
    background-image: linear-gradient(
        89.68deg,
        rgba(255, 255, 255, 0.5) 0.23%,
        rgba(255, 255, 255, 0.5) 99.82%
      ),
      url("../../../public/images/counter/counter-bg.png");
    background-repeat: no-repeat;
    background-size: 100%, cover;
    background-position: left top, right bottom;
    display: flex;
    gap: 24px;
    align-items: center;
    justify-content: space-between;
  }

  .counter__item {
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: -116px;
      height: 100px;
      width: 1px;
      background-color: #d9d9d9;
    }

    &:nth-last-of-type(1) {
      &::before {
        content: none;
      }
    }
  }

  .counter__item-inner {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 14px;
  }

  .h4 {
    color: #2b1b9a;
    display: flex;
    justify-content: center;
    gap: 10px;
    overflow: hidden;
    font-size: 30px;
    font-weight: 700;

    span {
      font-size: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
      color: inherit !important;
      overflow: visible !important;
      text-align: center !important;
    }
  }

  .h6 {
    text-transform: uppercase;
    font-weight: 500;
  }
}

// about two
.about-two {
  position: relative;
  overflow-x: clip;
  .about-two__thumb {
    position: relative;
    &::before {
      content: "";
      position: absolute;
      bottom: -80px;
      left: -30px;
      width: 100%;
      height: 100%;
      max-width: 600px;
      max-height: 600px;
      background-image: linear-gradient(
        180deg,
        #f0efff 0%,
        rgba(217, 217, 217, 0) 100%
      );
      z-index: -1;
      border-radius: 50%;
    }
  }

  .about__anime {
    img {
      position: absolute;
      z-index: -1;
    }

    .one {
      top: 50%;
      transform: translateY(-50%);
      left: 70px;
      max-width: 8vw;
      min-width: 50px;
      animation: smallMoveTwo 60s infinite linear;
    }

    .two {
      bottom: 17%;
      right: 100px;
      min-width: 40px;
      animation: smallMove 30s infinite linear;
    }
  }
}

// service two
.service-two {
  position: relative;
  overflow-x: clip;
  z-index: 1;
  &::before {
    content: "";
    position: absolute;
    inset: 0px;
    width: 100%;
    height: 80%;
    background-color: #a3d4f4;
    background-image: linear-gradient(88.9deg, #a3d4f4 0.95%, #e1deff 99.25%);
    z-index: -1;
  }

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 50px;
    width: 87px;
    height: 85px;
    z-index: -1;
    background: #4569e7;
    opacity: 0.25;
    border-radius: 1000px 1000px 0px 1000px;
    animation: smallMoveTwo 60s infinite linear;
  }

  .container {
    &::after {
      content: "";
      position: absolute;
      top: 20%;
      left: 85px;
      width: 87px;
      height: 85px;
      z-index: -1;
      background: #67b6f4;
      opacity: 0.25;
      border-radius: 1000px 1000px 0px 1000px;
      animation: smallMove 60s infinite linear;
    }
  }

  .thumb {
    width: 192px;
    height: 192px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .services__slider-single {
    border-radius: 10px;
    background-color: #e74545;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    padding: 130px 20px;

    img {
      transition: $transition;
    }

    &:hover {
      img {
        opacity: 0.25;
      }
      .services__slider-single__content {
        transform: scale(1);
        opacity: 1;
        visibility: visible;
      }
    }
  }

  .on {
    background-color: #ffcd1e;
  }

  .tw {
    background-color: #67b6f4;
  }

  .th {
    background-color: #4569e7;
  }

  .fo {
    background-color: #ff6c26;
  }

  .fi {
    background-color: #e74545;
  }
  .si {
    background-color: #9d91f5;
  }

  .services__slider-single__content {
    position: absolute;
    inset: 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    gap: 24px;
    padding: 65px 24px;
    z-index: 2;
    transform: scale(0);
    opacity: 0;
    visibility: hidden;
    transition: $transition;

    .h4 {
      color: $white;
      font-weight: 700;
      text-transform: capitalize;
    }

    a {
      @include box(84px);
      background-color: $white;
      color: $tertiary-color;
      font-size: 36px;
      outline: 1px solid $white;
      outline-offset: 12px;
    }

    .price-tag {
      padding: 10px 20px;
      background-image: url("../../../public/images/services/frame.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;

      p {
        text-transform: uppercase;
        font-size: 14px;
        line-height: 20px;
        font-weight: 700;
        color: $white;
        span {
          color: #e74545;
        }
      }
    }
  }
}

// feature
.feature-two {
  .feature__inner {
    padding: 40px;
    background-color: #f0efff;
    border-radius: 10px;
  }

  .feature__item-inner {
    max-width: 320px;
  }

  .thumb {
    margin-bottom: 20px;
  }

  .h5 {
    margin-bottom: 20px;
    font-weight: 700;
  }

  .swiper-slide-active {
    width: calc(100% - 640px);
  }
}

// free
.free {
  position: relative;
  overflow-x: clip;
  z-index: 1;

  .free__inner {
    padding: 65px;
    background: linear-gradient(96.38deg, #4569e7 0.21%, #b81aef 99.54%);
    box-shadow: 0px 4px 50px rgba(43, 27, 154, 0.15);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    &:hover {
      .thumb {
        &::before {
          transform: rotate(-20deg) translateX(35px) scaleY(0.7);
        }
      }
    }
  }

  .h6,
  .h2,
  p {
    color: $white;
  }

  .btn {
    background-color: #f0efff;
    padding: 12px 20px;
    color: #2b1b9a;
    border-radius: 5px;
    outline-offset: 8px;
    outline: 1px dashed #ffffff !important;
    margin: 8px;
  }

  .price-tag {
    padding: 10px 20px;
    background-image: url("../../../public/images/services/frame.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    p {
      text-transform: uppercase;
      font-size: 14px;
      line-height: 20px;
      font-weight: 700;
      color: $white;
      span {
        color: #e74545;
      }
    }
  }

  .thumb {
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    width: calc(100% - 48%);
    text-align: right;
    &::before {
      content: "";
      position: absolute;
      left: 0px;
      bottom: 0px;
      top: 0px;
      width: 25px;
      height: 100%;
      background-color: #1aefd5;
      transform: rotate(-20deg) translateX(35px) scaleY(1.5);
      transition: $transition;
    }
    &::after {
      content: "";
      position: absolute;
      left: 0px;
      bottom: 0px;
      top: 0px;
      width: 8px;
      height: 100%;
      background-color: #1aefd5;
      transform: rotate(-20deg) translateX(18px) scaleY(1.5);
      transition: $transition;
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
}

// testimonial
.testimonial-two {
  background-color: #f8f6fa;
  position: relative;
  z-index: 1;
  overflow-x: clip;

  .testimonnial-two__slider-item__thumb {
    position: relative;
    img {
      border-radius: 1000px 1000px 0px 0px;
    }

    .trust {
      position: absolute;
      left: 0px;
      top: 70%;
      transform: translateY(-70%);
      img {
        border-radius: 0px;
        max-width: 100%;
      }
    }
  }

  .quote {
    margin-bottom: 26px;
    i {
      font-size: 40px;
      color: #4569e7;
    }
  }

  .testimonnial-two__slider-item__content-meta {
    display: flex;
    align-items: center;
    gap: 26px;
    row-gap: 16px;
    flex-wrap: wrap;
    margin-top: 26px;
  }

  .thumb {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }

  .content {
    .h5 {
      color: $theme-color;
      text-transform: capitalize;
    }
    p {
      color: #818181;
    }
  }

  .col-12 {
    position: relative;
  }

  .testimonial-two-pagination {
    position: absolute;
    top: 83%;
    right: 240px;
    transform: translateY(-50%);
  }

  .anime {
    img {
      position: absolute;
      z-index: -1;
    }
  }

  .one {
    bottom: 0px;
    left: 0px;
    min-width: 300px;
  }

  .two {
    top: 80px;
    right: 80px;
    min-width: 50px;
    animation: smallMoveTwo 60s infinite linear;
  }
}

// pricing two
.pricing-two {
  .section__header--alt {
    .btn {
      background-color: #f0efff;
      border-color: #2b1b9a !important;
      color: #2b1b9a;
      border-radius: 5px;
      text-transform: uppercase;
      &:hover {
        color: $white;
      }
    }
  }

  .pricing-two__single {
    padding: 30px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    transition: $transition;

    .h5 {
      color: #1aefd5;
      text-transform: uppercase;
      font-weight: 700;
      transition: $transition;
    }

    &:hover {
      background-color: $theme-color;
      border: 1px solid #414141;

      p,
      .h2 {
        color: $white;
      }

      .meta {
        p {
          background-color: #020202;
        }
      }

      li {
        color: $white;
        i {
          color: $tertiary-color;
        }
      }

      hr {
        border-color: #ffffff !important;
      }

      .btn {
        color: $white;
        background-color: $tertiary-color;
        &::before {
          width: 100%;
        }
      }
    }
  }

  .meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    margin-top: 12px;
    flex-wrap: wrap;

    .thumb {
      min-width: 50px;
    }

    .h2 {
      margin-bottom: 10px;
      transition: $transition;
    }

    p {
      display: inline;
      padding: 6px 8px;
      font-size: 14px;
      text-transform: uppercase;
      background: rgba(16, 16, 16, 0.1);
      border-radius: 100px;
      transition: $transition;
    }
  }

  hr {
    width: 100%;
    height: 1px;
    background-color: #d9d9d9;
    margin: 30px 0px;
    opacity: 0.3;
    transition: $transition;
  }

  li {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    text-transform: capitalize;
    transition: $transition;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
    i {
      color: #a6a6a6;
      transition: $transition;
    }
  }

  .btn {
    border-radius: 50px;
    text-transform: capitalize;
    padding: 12px 26px;
  }

  .yel {
    color: #fbbc04 !important;
  }

  .pri {
    color: #fd2ac2 !important;
  }

  .tri {
    color: #4569e7 !important;
  }
}

// faq
.faq {
  background-image: linear-gradient(88.9deg, #a3d4f4 0.95%, #e1deff 99.25%);
}

.accordion {
  .accordion-item {
    margin-bottom: 10px;
    box-shadow: $shadow;
    background-color: $white;
    border-radius: 10px;
    border: 0px solid transparent;

    &:last-of-type {
      margin-bottom: 0px;
    }

    .accordion-button:not(.collapsed) {
      color: $tertiary-color;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 10px 10px 0px 0px;

      &::after {
        color: $tertiary-color;
        content: "\f068";
        font-family: "Font Awesome 6 Free";
      }
    }

    .accordion-button {
      border-radius: 10px;
      color: $theme-color;
      position: relative;
      box-shadow: 0px 0px 0px;

      &::after {
        content: "\2b";
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        background-image: none;
        font-size: 16px;
        color: $theme-color;
        z-index: 1;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .faq-one-active {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid #ffffff;
    box-shadow: 0px 4px 45px 6px rgba(69, 105, 231, 0.12);
  }

  h5 {
    display: flex;
    align-items: center;
    gap: 16px;
    text-transform: uppercase;

    button {
      padding: 0px;
      font-size: 20px;
      line-height: 26px;
      font-weight: 500;
      border: none;
      outline: none;
      box-shadow: 0px 0px 0px;
      padding: 32px 35px;
      text-transform: uppercase;
    }
  }

  .accordion-body {
    padding: 0px 35px 30px;
    border-top: 0px solid transparent;

    p {
      font-size: 18px;
      line-height: 26px;
      color: $theme-color;
      text-transform: capitalize;
    }
  }
}

// project
.project {
  background-image: linear-gradient(88.9deg, #a3d4f4 0.95%, #e1deff 99.25%);

  .section__header--alt {
    .btn {
      background-color: #f0efff;
      border-color: #2b1b9a !important;
      color: #2b1b9a;
      border-radius: 5px;
      &:hover {
        color: $white;
      }
    }
  }

  .paragraph {
    max-width: 820px;
    margin-left: auto;
    margin-right: auto;
  }

  .project__thumb {
    margin-bottom: 65px;
  }

  .btn {
    border-radius: 50px;
  }
}

// news two
.news-two {
  .section__header--alt {
    .btn {
      background-color: #f0efff;
      border-color: #2b1b9a !important;
      color: #2b1b9a;
      border-radius: 5px;
      text-transform: uppercase;
      &:hover {
        color: $white;
      }
    }
  }

  .news-two__content {
    padding: 30px 30px 30px 40px;
    background: #f0efff;
    border-radius: 10px;
    height: 100%;

    .news-two-pagination {
      position: relative;
      top: -40px;
      margin-bottom: -40px;
      z-index: 2;
      .slick-dots {
        justify-content: flex-start;
        button {
          border-radius: 50%;
        }
      }

      .swiper-pagination-bullet {
        border-radius: 50%;
      }
    }
  }

  .news-two__alt {
    padding: 30px;
    background: #f0efff;
    border-radius: 10px;
    height: 100%;

    .thumb {
      margin-bottom: 20px;
      a {
        width: 100%;
        border-radius: 10px;
      }

      img {
        height: auto;
        width: 100%;
        border-radius: 10px;
      }
    }

    .h4 {
      a {
        &:hover {
          color: $tertiary-color;
        }
      }
    }

    .meta {
      margin-top: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 16px;
      flex-wrap: wrap;

      a {
        color: $tertiary-color;
        text-transform: capitalize;
        font-size: 16px;
        &:hover {
          color: $theme-color;
        }
      }

      span {
        text-transform: capitalize;
        color: #424b5a;
      }
    }
  }

  .news-two__slider-item {
    display: flex;
    gap: 40px;

    .content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      flex-direction: column;
      gap: 100px;
      height: calc(100% - 80px);
      width: calc(100% - 457px);

      p {
        &:nth-last-of-type(1) {
          span {
            font-weight: 700;
            margin-right: 20px;
          }
        }

        a {
          color: $tertiary-color;
          &:hover {
            color: $theme-color;
          }
        }
      }
    }

    .thumb {
      width: 457px;
      height: 100%;
      a {
        width: 100%;
        border-radius: 10px;
      }

      img {
        width: 100%;
        height: auto;
        border-radius: 10px;
      }
    }

    .h4 {
      a {
        color: $theme-color;
        &:hover {
          color: $tertiary-color;
        }
      }
    }
  }
}

// service three
.service-three {
  margin-top: 90px;
  .services__slider-single {
    background-color: transparent;
    transition: $transition;
    position: relative;
    z-index: 1;
    &::before {
      content: "";
      position: absolute;
      inset: 0px;
      border-radius: 10px;
      width: 100%;
      height: 100%;
      background-color: #ffcd1e;
      z-index: -1;
      transform: scale(0);
      transition: $transition;
    }

    &:hover {
      &::before {
        transform: scale(1) !important;
      }
    }
  }

  .on {
    border: 1px solid #ffcd1e;
    &::before {
      background-color: #ffcd1e;
    }
  }

  .tw {
    border: 1px solid #67b6f4;
    &::before {
      background-color: #67b6f4;
    }
  }

  .th {
    border: 1px solid #4569e7;
    &::before {
      background-color: #4569e7;
    }
  }

  .fo {
    border: 1px solid #ff6c26;
    &::before {
      background-color: #ff6c26;
    }
  }

  .fi {
    border: 1px solid #e74545;
    &::before {
      background-color: #e74545;
    }
  }

  .swiper-slide-active {
    &::before {
      transform: scale(1) !important;
    }
  }
}

// quality three
.quality-three {
  background-color: #f1f4ff;

  .quality-three__single {
    position: relative;

    span {
      font-size: 16px;
      line-height: 22px;
      padding: 5px 10px;
      text-transform: capitalize;
      background-color: $white;
      border-radius: 30px;
      position: absolute;
      bottom: 30px;
      left: 30px;
    }
  }
}

// project three
.project-three {
  background-image: linear-gradient(90deg, #e8edff 0%, #c9eaff 100%);

  .project-three__slider-item {
    padding: 20px;
    background-color: $white;
    border-radius: 10px;
    max-height: 220px;
    width: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    img {
      width: 100%;
    }
  }
}

.faq-two {
  .faq-two__thumb {
    margin-right: -50px;
    img {
      transform: translateX(-80px);
    }
  }

  .accordion-button {
    text-transform: capitalize;
  }

  .faq-two-active {
    background: rgba(255, 255, 255, 0.1);
    border: 3px solid #4569e7;
    box-shadow: 0px 4px 45px 6px rgba(69, 105, 231, 0.12);
  }
}

.sponsor-three {
  position: relative;
  z-index: 1;
  overflow-x: clip;
  &::before {
    content: "";
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    width: 100%;
    height: 120px;
    background-color: $primary-color;
    z-index: -1;
  }
  &::after {
    content: "";
    position: absolute;
    bottom: 0px;
    left: 0px;
    right: 0px;
    width: 100%;
    height: calc(100% - 120px);
    background-color: #f8f6fa;
    z-index: -1;
  }
}

.sponsor-three__inner {
  padding: 65px 46px;
  background: #ffffff;
  box-shadow: 0px 4px 100px rgba(231, 69, 69, 0.2);
  border-radius: 10px;

  .swiper-slide {
    text-align: center;
    img {
      opacity: 0.15;
    }
  }

  .swiper-slide-active {
    img {
      opacity: 1;
    }
  }
}

.news__two-slider {
  .thumb {
    a {
      img {
        min-height: 220px;
      }
    }
  }
}

// about overview
.about-overview {
  position: relative;
  top: -350px;
  margin-bottom: -350px;
  z-index: 9;
  .about-overview__single {
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .gaper {
    &:nth-last-of-type(1) {
      margin-top: 70px;
    }

    .h2 {
      max-width: 420px;
    }

    .h6 {
      color: $primary-color;
      font-weight: 600;
      font-size: 16px !important;
    }
  }
}

.sponsor-alt {
  background-color: $white;
  &::before,
  &::after {
    content: none;
  }
}

// team
.team-two {
  overflow-x: clip !important;
  position: relative;
  z-index: 1;
  .paragraph {
    p {
      font-size: 18px;
    }
  }

  .team-two__slider-item {
    position: relative;
    width: 100%;
    overflow: hidden;
    &:hover {
      &::before {
        transform: translateY(0px);
      }

      .content {
        transform: translateY(0px);
      }
    }
    &::before {
      content: "";
      position: absolute;
      inset: 0px;
      top: unset;
      width: 100%;
      height: 80%;
      background-image: linear-gradient(
        180deg,
        rgba(69, 105, 231, 0) 0%,
        #4569e7 100%
      );
      transition: all 0.6s cubic-bezier(0.075, 0.82, 0.165, 1);
      transform: translateY(100%);
    }
    img {
      width: 100%;
      height: auto;
      min-height: 240px;
    }

    .content {
      position: absolute;
      bottom: 40px;
      left: 0px;
      right: 0px;
      max-width: 100%;
      text-align: center;
      transform: translateY(200px);
      transition: all 0.6s cubic-bezier(0.075, 0.82, 0.165, 1);
      .h4,
      p {
        color: $white;
      }

      .h4 {
        margin-bottom: 15px;
      }
    }
  }
}

.faq-three {
  .faq-two__thumb {
    margin-right: 0px;
    img {
      transform: translateX(80px);
    }
  }
}

// trial
.trial {
  background-color: $primary-color;
  overflow-x: clip;
  .section__header {
    text-align: center;
    p,
    h2 {
      color: $white;
    }

    .paragraph {
      margin-top: 24px;
      max-width: 820px;
      margin-left: auto;
      margin-right: auto;
    }
  }

  .trial__thumb {
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      border-radius: 10px;
    }
  }

  .trial__form {
    height: 100%;
    padding: 40px 30px 30px;
    background-color: $white;
    border-radius: 10px;

    .btn {
      width: 100%;
    }

    .group-radio {
      input {
        background-color: #e3e3e3;
        border-radius: 2px;
        border: 0px;
        &::before {
          content: none;
        }

        &:checked {
          background-color: $tertiary-color;
          &::before {
            content: "";
          }
        }
      }
    }

    .select-services {
      width: 100%;
      height: auto;
      padding: 12px 30px;
      line-height: 0px;
      float: unset;

      &::after {
        width: 10px;
        height: 10px;
        margin-top: -8px;
        position: absolute;
        top: 50%;
        right: 24px;
      }

      .list {
        width: 100%;
      }

      .current {
        color: #818181;
      }
    }
  }
}

.video-modal-two {
  position: relative;
  overflow: hidden;
  img {
    width: 100%;
    height: auto;
    min-height: 260px;
  }

  .video-wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .video-btn {
    background-color: $white;
    @include box(130px);
    color: $primary-color;
  }
}

.portfolio {
  overflow-x: clip;
  position: relative;
  z-index: 1;
  padding-top: 80px !important;

  .portfolio__filter-btns {
    display: flex;
    align-items: center;
    gap: 26px;
    row-gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 60px;

    button {
      font-size: 16px;
      font-weight: 500;
      color: $theme-color;
      padding: 6px;
      text-transform: capitalize;
      letter-spacing: 0px;
      border-radius: 100px;
      &:hover {
        color: $tertiary-color;
        background: rgba(69, 105, 231, 0.1);
      }
    }

    .active {
      color: $tertiary-color;
      background: rgba(69, 105, 231, 0.1);
    }
  }

  .portfolio__single-item {
    margin-bottom: 30px !important;

    img {
      width: 100%;
      min-height: 240px;
    }
  }

  .grid {
    margin-bottom: -30px;
  }

  .portfolio__single-item {
    position: relative;
    width: 100%;
    background-color: #b9acac10;
  }
}

// pricing
.pricing-main {
  .pricing-main__single {
    padding: 40px;
    background-color: $white;
    box-shadow: 0px 4px 70px rgba(69, 105, 231, 0.15);
    border-radius: 10px;
    text-align: center;
    height: 100%;
    position: relative;
    overflow: hidden;
    z-index: 1;

    &::before {
      content: "";
      position: absolute;
      inset: 0px;
      width: 100%;
      height: 100%;
      background-color: $tertiary-color;
      transform: translateY(100%);
      transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      z-index: -1;
    }

    &:hover {
      &::before {
        transform: translateY(0px);
      }

      .btn--secondary {
        background-color: $white;
        color: $tertiary-color;
        border: 1px solid $white;
      }

      .h4,
      p,
      span,
      strong {
        color: $white !important;
      }

      .anime {
        img {
          visibility: visible;
          opacity: 1;
          &:nth-of-type(1) {
            visibility: hidden;
            opacity: 0;
          }
        }
      }
    }
  }

  .thumb {
    margin-bottom: 30px;
    img {
      width: 142px;
      height: 142px;
    }
  }

  .h4 {
    margin-bottom: 30px;
    font-weight: 700;
    transition: $transition;
  }

  .paragraph {
    p {
      transition: $transition;
      &:nth-last-of-type(1) {
        font-weight: 700;
        strong {
          color: $primary-color;
          transition: $transition;
        }
        span {
          font-size: 14px;
          margin-left: 10px;
          color: #818181;
          font-weight: 400;
          transition: $transition;
        }
      }
    }
  }

  .btn--secondary {
    &::before {
      background-color: $white;
    }
    &:hover {
      color: $tertiary-color;
      border: 1px solid $white;
      background-color: $white;
    }
  }

  .anime {
    img {
      position: absolute;
      top: 40px;
      left: 40px;
      max-width: 60px;
      transition: $transition;
      z-index: -1;
      &:nth-last-of-type(1) {
        visibility: hidden;
        opacity: 0;
      }
    }
  }
}

// recent project
.recent-project {
  background-image: linear-gradient(90deg, #e8edff 0%, #c9eaff 100%);
  position: relative;
  overflow-x: clip;
  z-index: 1;

  .recent-project__inner {
    padding: 100px 80px;
    background-color: $white;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    z-index: 1;

    &::before {
      content: "";
      position: absolute;
      inset: 0px;
      width: 37%;
      height: 100%;
      background-color: $primary-color;
      z-index: -1;
    }
  }

  .recent-project__slider-item-inner {
    display: flex;
    align-items: center;
    gap: 100px;
    row-gap: 40px;

    .thumb {
      width: 588px;
      min-width: 588px;
      img {
        width: 100%;
      }
    }

    .content {
      width: calc(100% - 588px);
    }
  }

  .price-tag {
    padding: 10px 20px;
    background-image: url("../../../public/images/services/frame.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: inline-flex;
    margin-top: 40px;

    p {
      text-transform: uppercase;
      font-size: 14px;
      line-height: 20px;
      font-weight: 700;
      color: $white;
      span {
        color: #e74545;
      }
    }
  }
}

// quote
.quote-overview {
  position: relative;
  overflow: hidden;
  z-index: 1;
  .quote__counter {
    display: flex;
    align-items: center;
    gap: 24px;
    justify-content: space-between;
    margin-top: 40px;
  }

  .single {
    .h4 {
      font-weight: 700;
    }
    p {
      margin-top: 5px;
      text-transform: capitalize;

      color: #818181;
    }
  }

  .quote-anime {
    position: absolute;
    top: 15%;
    left: 45%;
    transform: translateX(-45%);
    z-index: -1;
    max-width: 100px;
    min-width: 60px;
  }

  .quote-overview__thumb {
    img {
      width: 100%;
    }
  }

  .h4 {
    display: flex;
    justify-content: flex-start;
    gap: 6px;
    overflow: hidden;
    font-weight: 700;

    span {
      font-size: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
      color: inherit !important;
      overflow: visible !important;
      text-align: center !important;
    }
  }
}

.custom-quote {
  position: relative;
  overflow: hidden;
  z-index: 1;
  background-image: linear-gradient(90deg, #e8edff 0%, #c9eaff 100%);
  .trial__form {
    padding: 40px 30px 30px;
    background-color: $white;
    border-radius: 10px;
    height: 100%;
    .btn {
      width: 100%;
    }

    .group-radio {
      input {
        background-color: #e3e3e3;
        border-radius: 2px;
        border: 0px;
        &::before {
          content: none;
        }

        &:checked {
          background-color: $tertiary-color;
          &::before {
            content: "";
          }
        }
      }
    }

    .select-services {
      width: 100%;
      height: auto;
      padding: 12px 30px;
      line-height: 0px;
      float: unset;

      &::after {
        width: 10px;
        height: 10px;
        margin-top: -8px;
        position: absolute;
        top: 50%;
        right: 24px;
      }

      .list {
        width: 100%;
      }

      .current {
        color: #818181;
      }
    }
  }

  .custom-quote__left,
  .custom-quote__right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 35px !important;
    .single {
      img {
        width: 100%;
      }
    }
  }

  .custom-quote__left,
  .custom-quote__right {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    animation: comunity 40s linear infinite;
    -webkit-animation: comunity 40s linear infinite;
    -moz-animation: comunity 40s linear infinite;
    .single {
      &:nth-last-of-type(1) {
        margin-bottom: 30px !important;
      }
    }
  }

  .custom-quote__right {
    animation-direction: alternate-reverse;
  }

  .quote-wrapper {
    overflow: hidden;
    height: 1424px;
    position: relative;
  }

  .custom-quote__left2,
  .custom-quote__right2 {
    top: -100%;
  }
  .custom-quote__left3,
  .custom-quote__right3 {
    top: 100%;
  }
}

@keyframes comunity {
  0% {
    transform: translateY(100%);
    opacity: 1;
  }
  50% {
    transform: translateY(0);
    opacity: 0.9;
  }
  99.99% {
    transform: translateY(-100%);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.quote-instructions {
  position: relative;
  z-index: 1;
  overflow: hidden;

  .gaper {
    row-gap: 60px;
  }

  .quote-instructions__single {
    text-align: center;

    .thumb {
      @include box(90px);
      background-color: #f1f4ff;
      margin-left: auto;
      margin-right: auto;
      .thumb-inner {
        @include box(64px);
        background-color: $tertiary-color;
      }
      span {
        font-size: 20px;
        color: $white;
        font-weight: 500;
      }
    }

    .content {
      margin-top: 20px;
      .h5 {
        margin-bottom: 25px;
        font-weight: 600;
      }
    }
  }
}

// team main
.team-main {
  .section__header {
    img {
      width: 100%;
      min-height: 250px;
      height: auto;
      border-radius: 10px;
    }
  }

  .team-main__single {
    position: relative;
    overflow: hidden;
    z-index: 1;
    height: 100%;
    &:hover {
      .single {
        transform: translateY(0px);
      }
    }

    img {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      min-height: 240px;
    }
  }

  .single {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    flex-wrap: wrap;
    position: absolute;
    bottom: 30px;
    left: 30px;
    right: 30px;
    max-width: 100%;
    padding: 20px;
    background-color: $white;
    border-radius: 10px;
    transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
    transform: translateY(150%);

    p {
      color: #818181;
      margin-top: 6px;
      font-size: 14px;
      font-weight: 400;
    }
  }

  .social {
    gap: 8px;
    a {
      @include box(34px);
      font-size: 14px;
      border: 1px solid #d9d9d9;
      background-color: $white;
      &:hover {
        background-color: $tertiary-color;
        color: $white;
      }
    }
  }
}

// contact
.contact-main {
  background-color: #fafafa;
  .contact-main__thumb {
    height: 100%;
    padding: 65px;
    background: linear-gradient(135deg, #4569e7 0%, #b81aef 100%);
    border-radius: 10px;
  }

  .content {
    .h2,
    p {
      color: $white;
    }

    .paragraph {
      margin-top: 50px;
    }
  }

  .thumb {
    margin: 70px 0px 70px;
    img {
      max-width: 70px;
    }
  }

  .single-group {
    li {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      &:nth-last-of-type(1) {
        margin-bottom: 0px;
      }
      a {
        font-weight: 500;
        color: $white;
      }
      i {
        @include box(40px);
        border-radius: 0px;
        background-color: rgba(255, 255, 255, 0.08);
        font-size: 16px;
        color: $white;
      }
    }
  }

  .contact-main__content {
    height: 100%;
    padding: 65px;
    background-color: $white;
  }
  .group-input {
    border-bottom: 1px solid #d9d9d9;
    margin-bottom: 40px;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
    input,
    textarea {
      color: $theme-color;
      text-transform: capitalize;
      width: 100%;
      min-width: 100%;

      &::placeholder {
        color: $theme-color;
      }
    }

    textarea {
      height: 120px !important;
      max-height: 120px !important;
      min-height: 120px !important;
    }
  }

  .subject {
    width: 100%;
    float: unset;
    border: 0px;
    height: auto;
    line-height: 28px;
    padding: 0px 20px 20px 0px;
    &::after {
      position: absolute;
      top: 50%;
      right: 20px;
      width: 10px;
      height: 10px;
      margin-top: -8px;
      border-color: $theme-color;
    }

    .list {
      width: 100%;
    }
  }

  .group-radio {
    input {
      border: 1px solid $tertiary-color;
    }
  }

  .btn {
    border-radius: 10px;
  }
}

.map-wrapper {
  max-height: 580px;

  iframe {
    width: 100%;
    height: 580px;
  }
}

// blog
.blog-main {
  .blog-main__single {
    margin-bottom: 65px;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }
    .thumb {
      padding: 30px;
      border: 1px solid #d9d9d9;
      border-radius: 10px;
      .thumb-link {
        a {
          width: 100%;
          img {
            width: 100%;
            min-height: 240px;
          }
        }

        position: relative;

        .video-wrap {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          a {
            @include box(104px);
            background-color: $white;
            color: $theme-color;
          }
        }
      }
    }

    .meta {
      display: flex;
      align-items: center;
      gap: 24px;
      row-gap: 16px;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-top: 30px;
    }

    .meta__left {
      display: flex;
      align-items: center;
      gap: 24px;
      span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #d9d9d9;
      }

      strong {
        color: $theme-color;
      }

      p {
        font-size: 14px;
        color: #646464;
      }
    }

    .meta__right {
      flex-grow: 1;
      display: flex;
      align-items: center;
      gap: 10px;
      justify-content: flex-end;
      a {
        width: auto;

        padding: 6px 10px;
        border-radius: 30px;
        color: #646464;
        background-color: #eeeeee;
        &:hover {
          background-color: #dff0fa;
        }
      }
    }

    .content {
      padding: 30px 30px 0px;

      .h4 {
        margin-bottom: 20px;
      }

      a {
        letter-spacing: 0px;
        &:hover {
          color: $primary-color;
        }
      }

      .cta {
        margin-top: 30px;
        a {
          @include box(42px);
          border-radius: 0px;
          background-color: #f5f5f5;
          font-size: 18px;
          color: $theme-color;
          &:hover {
            background-color: $primary-color;
            color: $white;
          }
        }
      }
    }
  }

  .blog-main__sidebar {
    background-color: #fafafa;
    padding: 30px;
  }

  .widget {
    margin-bottom: 40px;
    &:nth-last-of-type(1) {
      margin-bottom: 0px;
    }

    .widget__head {
      margin-bottom: 30px;
      .h5 {
        font-weight: 500;
      }
    }

    .form-group-input {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: 1px solid #d9d9d9;
      background-color: $white;
      padding-right: 20px;
      input {
        width: 100%;
        padding: 12px 20px 12px 20px;
        background-color: $white;
        color: #969696;
      }

      button {
        font-size: 20px;
        color: #646464;
        &:hover {
          color: $primary-color;
        }
      }
    }
  }

  .widget__list {
    li {
      list-style-type: disc;
      margin-bottom: 14px;
      &:nth-last-of-type(1) {
        margin-bottom: 0px;
      }
    }

    a {
      font-size: 16px;
      color: #0e202a;
      font-weight: 500;
      &:hover {
        color: $primary-color;
      }
    }
  }

  .widget__latest {
    .latest-single {
      display: flex;
      align-items: center;
      gap: 20px;
      padding-bottom: 30px;
      border-bottom: 1px solid #d9d9d9;
      margin-bottom: 30px;
      &:nth-last-of-type(1) {
        margin-bottom: 0px;
        padding-bottom: 0px;
        border: 0px solid transparent;
      }
    }

    .latest-thumb {
      width: 80px;
      height: 80px;
      min-width: 80px;
      a {
        width: 100%;
      }
      img {
        width: 100%;
      }
    }

    .latest-content {
      p {
        color: #646464;
      }

      a {
        color: $theme-color;
        font-size: 16px;
        font-weight: 500;
        text-transform: capitalize;
        letter-spacing: 0px;
        &:hover {
          color: $primary-color;
        }
      }
    }
  }

  .widget__tags {
    ul {
      display: flex;
      align-items: center;
      gap: 24px;
      justify-content: space-between;
      flex-wrap: wrap;
      a {
        font-size: 14px;
        color: #646464;
        text-transform: capitalize;
        &:hover {
          color: $primary-color;
        }
      }
    }
  }

  .widget-big {
    a {
      width: 100%;
      img {
        width: 100%;
        min-height: 200px;
      }
    }
  }
}

.blog-details {
  .bd-thumb {
    img {
      width: 100%;
      min-height: 240px;
    }
  }

  .bd-content {
    padding: 30px;
  }

  .bd-meta {
    margin-bottom: 30px;
    .meta__left {
      display: flex;
      align-items: center;
      gap: 24px;
      span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #d9d9d9;
      }

      strong {
        color: $theme-color;
      }

      p {
        font-size: 14px;
        color: #646464;
      }
    }
  }

  .bd-content-info {
    .paragraph {
      margin-top: 20px;
      margin-bottom: 50px;
    }
  }

  .bd-group {
    display: flex;
    gap: 16px;
    img {
      width: calc(50% - 8px);
      height: 100%;
    }
  }

  .bd-content__alt {
    margin-top: 30px;
    ul {
      margin-top: 30px;
    }
    li {
      list-style-type: disc;
      margin-bottom: 10px;
      &:nth-last-of-type(1) {
        margin-bottom: 0px;
      }
    }
  }

  .bd-quote {
    padding: 48px 80px;
    background-color: #f3f6ff;
  }

  .bd-tags {
    padding: 30px 0px;
    border: 1px solid #d9d9d9;
    border-left: 0px;
    border-right: 0px;
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 40px;

    p {
      color: #424b5a;
    }

    .tags-left,
    .tags-right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
    }

    .tags-left {
      .tags-content {
        display: flex;
        align-items: center;
        gap: 10px;

        a {
          font-size: 14px;
          color: #646464;
          padding: 6px 10px;
          border-radius: 30px;
          background-color: #eeeeee;
          &:hover {
            background-color: #dff0fa;
          }
        }
      }
    }

    .tags-right {
      justify-content: flex-end;
      .social {
        gap: 12px;
      }
      a {
        @include box(30px);
        border: 1px solid #d9d9d9;
        color: #d9d9d9;
        font-size: 12px;
        background-color: $white;
        &:hover {
          color: $tertiary-color;
          border: 1px solid $tertiary-color;
          background-color: $white;
        }
      }
    }
  }

  .blog-details__pagination {
    a {
      font-size: 16px;
      font-weight: 600;
      color: $theme-color;
      i {
        font-size: 24px;
      }
    }
    .single--alt {
      text-align: end;
    }

    .latest-single {
      padding: 25px 30px;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      gap: 20px;
      margin-top: 30px;

      .latest-thumb {
        width: 80px;
        min-width: 80px;
        height: 80px;
        img {
          width: 100%;
        }
      }

      .latest-content {
        text-align: start;
        p {
          color: #646464;
          font-size: 14px;
        }
        a {
          color: $theme-color;
          text-transform: capitalize;
          font-size: 16px;
          font-weight: 500;
          letter-spacing: 0px;
          &:hover {
            color: $primary-color;
          }
        }
      }
    }
  }
}

.comment-form {
  input,
  textarea {
    background-color: $white;
    border-radius: 0px;
  }

  textarea {
    min-height: 200px;
  }

  .cta__group {
    justify-content: flex-start;
    i {
      transform: rotate(-45deg);
    }

    .btn {
      background-color: $white;
      color: $theme-color;
      text-transform: capitalize;
      border-radius: 0px;
      border: 1px solid #d9d9d9 !important;
      &:hover {
        color: $white;
        border: 1px solid $tertiary-color !important;
      }
    }
  }

  .form-group-wrapper {
    margin-bottom: 30px;
  }
}

.audio-player {
  margin-top: 30px;
  audio {
    width: 100%;
  }
}

.thumb-radio {
  padding: 75px 40px;
  background: #f5f5f5;
}

.registration-popup-page {
  opacity: 1 !important;
  transform: scale(1) !important;
  visibility: visible !important;

  .registration-inner {
    flex-direction: column;
  }

  .back-to-home {
    margin-bottom: 60px;
    text-align: end;
    width: 100%;
    max-width: 1410px;
  }

  .back-home {
    color: $white;
    border: 1px solid $white !important;
    border-radius: 0px !important;
    &::before {
      background-color: $white;
    }
    &:hover {
      background-color: $white;
      color: $theme-color;
    }
  }
}

.alt-service-slider {
  margin-bottom: 40px;
}

.header {
  .rangu {
    img {
      min-height: 420px;
    }
  }
}

.quality-section {
  overflow-x: clip !important;
  .quality-section__tab-item {
    position: relative;
    width: 100%;
    display: none;
  }

  .quality-section__tab-item-active {
    display: block;
  }
  .rangu {
    img {
      min-height: 540px;
      max-height: 540px;
    }
  }
}

.service-de-thumb-alt {
  .about-section__thumb {
    height: 534px;
  }

  .rangu {
    position: relative;

    img {
      min-height: 540px;
      max-height: 540px;
    }
  }
}

.check-ready-single {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: space-between;
  .singlee {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 8px 0px;

    input {
      appearance: none;
      position: relative;
      width: 14px !important;
      height: 14px !important;
      padding: 0px;
      margin: 0px;
      background: #e3e3e3;
      border-radius: 2px;
      cursor: pointer;
      &:checked {
        background-color: $tertiary-color;
      }
    }

    label {
      font-size: 14px;
      line-height: 1;
      margin: 0px;
      text-transform: capitalize;
      cursor: pointer;

      color: #818181;
    }
  }
}

.try-cta {
  .btn--secondary {
    &::before {
      background-color: $white;
    }

    &:hover {
      color: $theme-color;
    }
  }
}

.accordion-collapse {
  height: 0;
  opacity: 0;
  visibility: hidden;
  overflow: hidden;
  display: block !important;
  transition: all 0.6s ease-in;
}

.accordion-collapse.show {
  height: auto;
  opacity: 1;
  visibility: visible;
  overflow: visible;
}

.grid-item.hidden {
  display: none;
}

/* ====
 --------- (5.0) all sections styles end ---------
 ==== */
